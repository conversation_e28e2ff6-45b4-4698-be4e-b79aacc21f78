@use 'sass:map';
@use 'base' as *;

// theme switcher
.theme-switcher {
  @apply relative inline-flex h-[28px] w-[52px] cursor-pointer items-center justify-center rounded-full bg-neutral-100 text-dark dark:bg-darkmode-theme-dark dark:text-white;
  &::after {
    @apply absolute z-10 h-[18px] w-[18px] rounded-full bg-white bg-no-repeat transition-[cubic-bezier(0.77_0_0.18_1)] content-[''];
    background-size: 6px 6px;
    background-position: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    //background-image: url("data:image/svg+xml,%3Csvg version='1.1' fill='%23848484' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 6 6' style='enable-background:new 0 0 6 6;' xml:space='preserve'%3E%3Cpath d='M5.5,6C5.2,6,5,5.8,5,5.5v-5C5,0.2,5.2,0,5.5,0S6,0.2,6,0.5v5C6,5.8,5.8,6,5.5,6z M0.5,6C0.2,6,0,5.8,0,5.5v-5 C0,0.2,0.2,0,0.5,0S1,0.2,1,0.5v5C1,5.8,0.8,6,0.5,6z'/%3E%3C/svg%3E");
  }

  .sun {
    @apply mr-[4px];
  }

  .moon {
    @apply -translate-y-[0.03rem];
  }
}

.dark .theme-switcher::after {
  @apply bg-darkmode-theme-light ;
}

// section style
.section {
  @apply py-16;
}

// container style
.container {
  @apply px-4 md:px-8;
  &-home {
    @apply pl-2;
    @media (min-width: 1536px) {
      max-width: 2560px;
    }
  }
}

// main wrrapper
.main {
  @apply flex-1 mt-0 px-0 py-1 dark:bg-darkmode-body;
}

// Reading progress bar
.reading-progress-bar {
  @apply fixed top-0 left-0 z-50 w-full h-1 bg-gray-200 dark:bg-gray-700;

  &-fill {
    @apply h-1 bg-gradient-to-r from-primary to-secondary transition-all duration-300 ease-out;
  }
}

// social icons
.social-icons-simple {
  // @apply space-x-5;
  li {
    @apply inline-block;
    a {
      @apply block p-3 leading-none text-white transition hover:text-primary dark:hover:text-darkmode-primary;
    }
  }
}

.social-icons {
  @apply space-x-5;
  li {
    @apply inline-block;
    a {
      @apply block text-dark hover:text-primary dark:text-darkmode-dark dark:hover:text-darkmode-primary;
    }
  }
}

// form style
.form-input,
.form-select {
  @apply h-16;
}

.form-input,
.form-select,
.form-textarea {
  @apply rounded border border-border bg-transparent transition duration-200 focus:border-primary focus:ring-transparent dark:border-darkmode-border;
}

select:required:invalid {
  @apply text-text;
}

.form-select {
  background-position: right center !important;
}

.form-label {
  @apply pointer-events-none absolute top-5 ml-4 inline-block origin-left bg-white px-1 text-text transition-all duration-200 dark:bg-darkmode-theme-dark dark:text-darkmode-text;
}

.form-select ~ .form-label {
  @apply bg-white;
}

.form-select,
.form-input,
.form-textarea {
  &.has-value ~ .form-label,
  &.invalid ~ .form-label,
  &.valid ~ .form-label,
  &:focus ~ .form-label {
    @apply -top-3 scale-90;
  }
}

// sidebar
.sidebar {
  @apply z-30 mt-3 sticky top-5 mr-4 min-h-full flex p-4 pl-0 pt-8 flex-col bg-white  h-[calc(100vh-50px)] min-w-[200px] w-[250px] overflow-y-auto dark:bg-darkmode-theme-dark;

  &.show {
    @apply translate-x-0;
  }
}

.sidebarfixed {
  @apply fixed top-0 z-50 flex min-h-full bg-white items-center
  h-[48px] w-[100%] max-w-[100%] -translate-x-full select-none flex-col overflow-y-hidden  py-1.5 px-3 transition-[transform_0.3_cubic-bezier(0.075_0.82_0.165_1)] dark:bg-darkmode-theme-dark lg:sticky lg:hidden xl:w-[100%];

  &.show {
    @apply translate-x-0;
  }
}

.sidebar-container {
  overflow-x: auto; /* Enable horizontal scrolling */
    max-width: 100%;
}
@media (max-width: 768px) { /* Adjust the max-width as per your mobile breakpoint */
  .sidebar-container {
      max-width: 100vw; /* Full viewport width on mobile */
  }
  .feature-item {
    min-width: 120px !important;
    padding: 8px 0 !important;
    margin-left: 8px;
  }
}

.toc {
  width: 100%;
}

.table-index, .toc {
  @apply max-h-[calc(100vh-150px)] overflow-y-auto text-dark rounded-lg z-50 w-full text-[0.9em] p-4 bg-white/90 dark:bg-darkmode-theme-dark/90 backdrop-blur-sm border border-gray-100 dark:border-gray-800;

  > ul {
    > li {
      @apply rounded relative mb-1 text-dark/70 dark:text-white/50;
      a {
        @apply line-clamp-2 block transition-all duration-300 py-1.5 px-2 rounded-md;

        &:hover {
          @apply bg-gray-50 dark:bg-gray-800/50 text-primary dark:text-primary;
        }
      }

      &.active > a {
        @apply text-primary dark:text-primary border-l-2 border-primary pl-3 bg-primary/5;
      }

      > ul {
        @apply ml-4 mt-1;
        list-style-type: '– ';

        li {
          @apply mb-1 text-[0.95em];

          &.active > a {
            @apply text-primary dark:text-primary border-l-2 border-primary pl-3 bg-primary/5;
          }
        }
      }
    }
  }
}

main {
  h2, h3 {
    @apply scroll-mt-24 relative;
    &::before {
      display: block;
      content: " ";
      visibility: hidden;
    }

    // Add anchor links on hover
    &:hover::after {
      content: '';
      @apply absolute -left-6 text-primary opacity-0 cursor-pointer transition-opacity duration-300;
    }

    &:hover::after {
      @apply opacity-70;
    }
  }
}

.home-hero {
  background: linear-gradient(
                  126deg,
                  #2f40ad 27.90508270263672%,
                  #394fb7 39.71376419067383%,
                  #2a3ba9 50.911521911621094%,
                  #394fb7 63.58194351196289%,
                  #354ab5 91.60547256469727%
  );
  &.hero2 {
    background: linear-gradient(126deg, #0b0d1a 11%, #080b1d 27%, #0c1546 70%, #1646a8 100%);
  }
  &.hero3 {
    background: linear-gradient(126deg, #312e81 0%, #3730a3 25%, #4338ca 50%, #4f46e5 75%, #6366f1 100%);
  }
  &.hero4 {
    background: linear-gradient(126deg, #12368C 0%, #1947A3 25%, #2058BA 50%, #2769D1 75%, #2E7AE8 100%);
  }
  &.hero5 {
   background: linear-gradient(126deg, #4c1d95 0%, #5b21b6 25%, #6d28d9 50%, #7c3aed 75%, #8b5cf6 100%);
  }

  h1,
  p {
    color: #fff;
  }

  p {
    font-size: 1.1em;
  }
}

// sidebar-toggler
.sidebar-toggler {
  @apply bg-white/50 rounded-lg border border-border dark:border-darkmode-border;
  &.fixed {
    @apply top-0 z-[99];
  }

  &-icon {
    @apply cursor-pointer select-none transition-[transform_0.4s];
    -webkit-tap-highlight-color: transparent;

    .top {
      stroke-dasharray: 40 121;
    }

    .bottom {
      stroke-dasharray: 40 121;
    }

    .line {
      @apply dark:stroke-white;
      fill: none;
      transition: stroke-dasharray 0.4s,
      stroke-dashoffset 0.4s;
      stroke: #333333;
      stroke-width: 5.5;
      stroke-linecap: round;
    }

    &.active {
      transform: rotate(45deg);

      .top {
        stroke-dashoffset: -68px;
      }

      .bottom {
        stroke-dashoffset: -68px;
      }
    }
  }
}

// sidebar-overlay
.sidebar-overlay {
  @apply pointer-events-none invisible fixed left-0 top-[64px] z-20 h-full w-full bg-black opacity-0 transition-all duration-300;
  &.show {
    @apply pointer-events-auto visible opacity-40;
  }
}

.theme-card-large {
  @apply flex h-full  flex-col justify-between pb-4 relative overflow-hidden bg-white dark:bg-darkmode-theme-dark;
  &-body {
    @apply p-6 pb-1;
  }

  &-footer {
    @apply mt-auto flex items-center px-6 pb-2;
  }
}

// theme card
.theme-card {
  @apply flex h-full  flex-col justify-between pb-4 relative overflow-hidden ;
  &-body {
    @apply p-6 pb-1;
  }

  &-footer {
    @apply mt-auto flex items-center px-6 pb-2;
  }
}

.github-content {
  img {
    @apply max-w-[500px];
  }
}

.slide-image {
  @apply h-[350px] w-auto m-2 mb-10 shadow-sm rounded overflow-hidden;
}
.slide-image2 {
  @apply h-[300px] w-auto m-2 mb-12 shadow-sm rounded overflow-hidden;
}

// tooltip
.tooltip {
  @apply relative inline-block cursor-pointer;
  &-label {
    @apply pointer-events-none invisible absolute bottom-[calc(100%+5px)] left-[65%] z-50 w-max max-w-[350px] -translate-x-1/2 rounded bg-primary px-4 py-3 text-left font-medium tracking-wider  text-sm text-white opacity-0 transition;
    &::after {
      @apply absolute left-1/2 top-full -translate-x-1/2 border-4 border-solid border-transparent border-t-primary content-[""];
    }
  }

  &:hover .tooltip-label {
    @apply visible opacity-100;
  }

  &-bottom {
    .tooltip-label {
      @apply bottom-[unset]  top-[calc(100%+5px)];
      &::after {
        @apply bottom-full top-[unset] border-b-primary border-t-transparent;
      }
    }
  }
}

// tooltip static which is not generated from js
.tooltip-static {
  @extend .tooltip;

  &-label {
    @extend .tooltip-label;
  }

  &-bottom {
    @extend .tooltip-bottom;
  }
}

// meta tags list in taxonomy single page
.meta-list {
  li {
    @apply mr-4 inline-flex items-center;
    a {
      @apply text-inherit hover:underline;
    }

    svg {
      @apply mr-1 text-primary dark:text-darkmode-primary;
    }
  }
}

// theme single page widgets
.widget {
  @apply mb-10 rounded;
  @media (max-width: 1023px) {
    @apply p-0;
  }
}

.feature-item {
  @apply p-1 border-b-2  border-b-blue-700/0 w-auto flex flex-col min-w-[80px] items-center justify-end mr-3 max-w-[200px] mb-1;
  .font-title {
    @apply px-2 rounded-2xl font-medium text-[0.8em];
  }
  &.active {
    //@apply border-b-blue-700 apply;
    .font-title {
      @apply bg-blue-700 text-white transition-all duration-300;
    }
  }
}

// sidebar checkbox button
.sidebar-checkbox {
  @apply relative mb-[4px] mx-3 ml-0 flex-row justify-between my-0 w-full flex cursor-pointer items-center rounded py-1 pr-4 pl-5 text-dark dark:text-white/90 dark:hover:bg-darkmode-theme-light;

  &::before {
    @apply absolute -left-2 top-1/2 h-4 w-4 -translate-y-1/2 rounded border-2 border-transparent content-[""];
  }

  &:hover::before {
    //@apply border-primary/60;
  }

  &::before {
    @apply ml-[10px] border-neutral-400 border-2 bg-cover bg-no-repeat;
    background-position: 50%;
  }

  &.active {
    &::before {
      @apply border-[#3474F4] bg-cover bg-no-repeat;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='%233474F4' d='M400 480H48c-26.51 0-48-21.49-48-48V80c0-26.51 21.49-48 48-48h352c26.51 0 48 21.49 48 48v352c0 26.51-21.49 48-48 48zm-204.686-98.059l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.248-16.379-6.249-22.628 0L184 302.745l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.25 16.379 6.25 22.628.001z' class=''%3E%3C/path%3E%3C/svg%3E");
      background-position: 50%;
    }
  }
}

.sidebar-heading {
  @apply flex mb-2 cursor-pointer items-center text-[0.8em] justify-between py-1 pl-0 font-primary text-[#2F3FAC] font-medium ;
}

// sidebar radio button
.sidebar-radio {
  @apply relative mb-1 flex w-full cursor-pointer items-center rounded py-1 pl-2 pr-3 text-sm text-dark hover:bg-white dark:hover:bg-darkmode-theme-light;
  &::before {
    @apply absolute right-3 top-3 h-3 w-3 rounded-full ring-2 ring-primary/60 content-[""];
  }

  &.active {
    @apply bg-white dark:bg-darkmode-theme-light;
    &::before {
      @apply border-2 border-white bg-primary ring-2 ring-primary dark:border-darkmode-border;
    }
  }
}

// themes category list
.category-list {
  li {
    @apply max-h-[40px] mb-3 mr-3 min-w-[90px] inline-block cursor-pointer select-none rounded-xl text-[0.9em] bg-white dark:bg-darkmode-theme-dark py-[.2rem] px-4 font-medium text-dark last:mr-0 dark:border-darkmode-border dark:text-darkmode-text;
    span {
      @apply ml-2 inline-block w-[27px] rounded bg-theme-light px-[.3rem] pt-[2px] text-center text-xs dark:bg-darkmode-theme-light;
    }

    &.active {
      @apply border-primary bg-primary text-white;
      span {
        @apply bg-dark/[.15] text-white;
      }
    }

    &.submit-btn {
      @apply bg-blue-200 text-blue-700;
    }
  }
}

.category-list {
  li {
    @apply mb-3 mr-3 min-w-[90px] inline-block cursor-pointer select-none rounded-xl text-[0.9em] bg-white dark:bg-darkmode-theme-dark py-[.2rem] px-4 font-medium text-dark last:mr-0 dark:border-darkmode-border dark:text-darkmode-text;
    span {
      @apply ml-2 inline-block w-[27px] rounded bg-theme-light px-[.3rem] pt-[2px] text-center text-xs dark:bg-darkmode-theme-light;
    }

    &.active {
      @apply border-primary bg-primary text-white;
      span {
        @apply bg-dark/[.15] text-white;
      }
    }

    &.submit-btn {
      @apply bg-blue-200 text-blue-700;
    }
  }
}

.category-list-white {
   scroll-snap-type: x;
   scroll-padding: 1rem;
   overflow-x: auto;
  scrollbar-width: none;
  li {
    white-space: nowrap;
    display: inline-table;
    line-height: 30px;
    @apply my-1 mb-[0.6rem] mr-3 cursor-pointer select-none rounded-xl  border border-white/10 py-[.2rem] px-4 font-medium text-white/80  transition;

    span {
      @apply ml-[6px] inline-block rounded text-white bg-white/10 px-[.3rem] py-[3px]  text-center text-xs dark:bg-darkmode-theme-light;
    }

    &.active {
      @apply text-white bg-white/[.15] border-white/80 scale-[1.05];
      span {
        @apply bg-white/[.15] text-white;
      }
    }

    &.submit-btn {
      @apply bg-white text-dark;
    }
  }
}

// sort dropdown
.sort-dropdown {
  @apply relative mt-1 inline-block whitespace-nowrap text-[.9rem];
  &-input {
    @apply ml-2 cursor-pointer rounded border border-border px-3 py-[.3rem] dark:border-darkmode-border;
  }

  &-buttons {
    @apply absolute right-0 top-8 z-50 hidden w-full rounded bg-white p-3 shadow dark:bg-darkmode-theme-dark;
    button {
      @apply block w-full rounded px-2 py-1 text-left;
      &:hover {
        @apply bg-theme-light dark:hover:bg-white/[.1];
      }

      &.active {
        @apply bg-primary/[.1] text-primary dark:bg-darkmode-theme-light dark:text-darkmode-primary;
        &:hover {
          @apply dark:bg-darkmode-theme-light;
        }
      }
    }

    &.show {
      @apply block;
    }
  }
}

// demo previewer
.demo-preview {
  &-switcher {
    @apply inline-block rounded text-[1.45rem] leading-none ;
    button {
      @apply rounded-md px-2 py-[.3rem];
      &.active {
        @apply bg-gray-700 text-white;
      }
    }
  }

  &-wrapper {
    @apply flex items-center justify-center bg-theme-light transition-all dark:bg-theme-dark;
    iframe {
      @apply mx-auto my-0 h-full w-full rounded-[inherit] border-0 shadow-none;
    }
  }

  &-content {
    @apply relative mx-auto h-full w-full overflow-hidden border-white bg-white transition-all duration-500 dark:border-dark dark:bg-darkmode-theme-light;
    &.tablet {
      @apply mt-[-15px] h-[1024px] max-h-[calc(100vh-130px)] w-[768px] rounded-lg border-[15px] border-white shadow dark:border-darkmode-border;
    }

    &.mobile {
      @apply mt-[-10px] h-[740px] max-h-[calc(100vh-120px)] w-[375px] rounded-lg border-[10px] border-white shadow dark:border-darkmode-border;
    }
  }
}

// cookie box
.cookie-box {
  @apply fixed bottom-3 left-3 z-50 mr-5 block w-[calc(100%-1.5rem)] max-w-[450px] items-center justify-between rounded bg-white px-5 py-4 shadow dark:bg-darkmode-theme-light md:bottom-5 md:left-5 md:flex;
  &-closer {
    @apply rounded border-0 bg-theme-light px-3 py-1 text-sm font-semibold transition hover:bg-primary hover:text-white dark:bg-darkmode-theme-dark;
  }
}

// bookmark box
.bookmark-box {
  @apply fixed bottom-5 left-5 z-50 mr-5 hidden origin-bottom-left items-center justify-between rounded bg-white py-1.5 pl-2 pr-5 shadow transition-[cubic-bezier(0.18_0.89_0.32_1.27)] dark:bg-darkmode-theme-light;
  &-icon {
    @apply mr-2.5 flex items-center justify-center rounded px-1.5 py-2;
  }

  &-closer {
    @apply absolute -right-2.5 -top-2.5 h-6 w-6 scale-0 cursor-pointer rounded-full bg-white text-center leading-6 shadow transition dark:bg-darkmode-theme-light;
  }

  &:hover {
    .bookmark-box-icon {
      @apply bg-theme-light dark:bg-darkmode-theme-dark;
    }

    .bookmark-box-closer {
      @apply scale-100;
    }
  }
}

// custom scroll-box
.scroll-box {
  @apply overflow-y-auto overflow-x-hidden;
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }

  &:hover {
    &::-webkit-scrollbar-track {
      @apply bg-[#f1f1f1] dark:bg-[#3b4b5a];
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-[#bbb] dark:bg-[#032020];
    }
  }
}

// modal
.modal {
  @apply fixed left-0 top-0 z-[50] h-screen w-screen bg-black/30 backdrop-blur-md;
  &-overlay {
    @apply absolute left-0 top-0 h-full w-full;
  }

  &-box {
    @apply relative mx-auto max-h-[calc(100vh-100px)] max-w-[calc(100%-20px)] overflow-hidden rounded-lg bg-white shadow-lg dark:bg-darkmode-theme-dark;
  }
}

// scroll-to-top
.scroll-to-top {
  @apply invisible fixed bottom-[0%] right-0 z-50 h-10 w-10 content-center items-center rounded-xl border-0 bg-gradient-to-r  text-white opacity-0 bg-black/20 hover:bg-dark outline-none transition-all sm:right-5;
  &.show {
    @apply visible bottom-[2%] opacity-100;
  }
}

// preview style
.browser-preview {
  @apply mx-auto w-full overflow-hidden rounded-md  transition-all hover:shadow-lg;

  &-frame {
    @apply h-[990px] w-full bg-white origin-top-left border-none;

    // xxl
    @media (max-width: 1535px) {
      @apply h-[990px] w-screen scale-[0.56];
    }

    // lg
    @media (max-width: 1279px) {
      @apply h-[999px] w-screen scale-[0.56];
    }

    // desktop
    @media (max-width: 1023px) {
      @apply h-[908px] w-screen scale-[0.95];
    }
  }

  &-mobile {
    @apply w-[360px];
    .browser-preview-frame {
      @apply h-full w-full scale-100;
    }
  }

  &-header {
    @apply relative flex items-center justify-between rounded-t bg-neutral-200 px-2 py-1;
    &::before {
      @apply relative left-6 top-0 inline-block h-2 w-2 rounded-full bg-[#ccc] shadow-[] content-[""];
      box-shadow: -18px 0 0 #ccc,
      18px 0 0 #ccc;
    }

    &-content {
      @apply absolute left-1/2 z-10 -translate-x-1/2 text-center text-sm dark:text-dark;
      &-icon {
        @apply relative -left-1 top-[1px] inline-flex h-4 w-4 items-center justify-center leading-none;
        animation: rotating 1s linear infinite;
      }

      &-hide {
        @apply invisible right-8 opacity-0;
      }
    }

    &-buttons {
      @apply invisible translate-x-8 text-right leading-none opacity-0 duration-300;
      &-show {
        @apply visible translate-x-0 opacity-100 delay-200;
      }

      &-link {
        @apply inline-block h-6 w-6 rounded bg-white text-center text-sm text-dark no-underline hover:text-primary dark:hover:text-darkmode-primary;
      }
    }
  }

  &-body {
    @apply relative h-[555px] overflow-hidden;
    box-shadow: inset 0 0 10px #eee;
    @media (max-width: 1279px) {
      @apply h-[500px];
    }
  }

  &-thumbnail {
    @apply absolute left-0 top-0 z-10 h-full w-full;
    img {
      @apply h-full w-full object-cover;
    }
  }

  &-loaded {
    @apply bg-[#d3f198];
  }

  &-after-loaded {
    @apply bg-[#ececec] delay-75;
  }

  &-toggler {
    @apply relative z-30 mb-8 space-x-1;
    button {
      @apply h-8 w-12 rounded border border-border bg-transparent focus:border-primary dark:border-darkmode-border;
    }
  }
}

.spotlight-card {
  @apply relative h-full bg-neutral-200 p-px before:absolute before:w-80 before:h-80 before:-left-40 before:-top-40 before:bg-[#166AE9] after:bg-[#166AE9] before:rounded-full before:opacity-0 before:pointer-events-none before:transition-opacity before:duration-500 before:translate-x-[var(--mouse-x)] before:translate-y-[var(--mouse-y)] before:group-hover:opacity-100 before:z-10 before:blur-[100px] after:absolute after:w-96 after:h-96 after:-left-48 after:-top-48  after:rounded-full after:opacity-0 after:pointer-events-none after:transition-opacity after:duration-500 after:translate-x-[var(--mouse-x)] after:translate-y-[var(--mouse-y)] after:hover:opacity-10 after:z-30 after:blur-[100px] overflow-hidden rounded-lg;

  > div {
    @apply h-full bg-white p-2 py-7 pb-5 rounded-[inherit] z-20 overflow-hidden;
  }
}

.block-attr {
  @apply mt-6 mr-4 block bg-neutral-100/60 border border-neutral-200 dark:bg-darkmode-theme-dark dark:border-theme-dark p-5 rounded-lg;
}

.table-att {
  @apply text-[0.85em] ;
  tr {
    @apply align-top;
    td:first-child {
      @apply block min-w-[130px] font-bold mb-4;
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

//mockup
.browser-mockup {
  @apply relative mb-8 rounded border-t-[35px] border-border shadow;

  img {
    @apply my-0 rounded-b;
  }

  &::before {
    @apply absolute -top-[21px] left-4 block h-2 w-2 rounded-full bg-[#f44] content-[''];
    box-shadow: 0 0 0 2px #fa5f57,
    1.2em 0 0 2px #fcbc2e,
    2.4em 0 0 2px #28c840;
  }

  &::after {
    @apply absolute -top-[30px] right-1/2 block w-[calc(100%_-_200px)] max-w-[500px] translate-x-1/2 overflow-hidden rounded bg-body p-1.5 text-center text-sm leading-none text-light content-['https://inspireui.com'];
  }
}

.demo-intro {
  @apply pt-2 pr-6 text-[0.95em] font-medium block overflow-hidden leading-8 text-left text-ellipsis break-words max-h-[9em];

  .screenshot {
    @apply w-1/2 flex flex-row h-[400px];

    img {
      @apply mx-1.5 h-[200px];
    }
  }
}

// content style
.content {
  @apply prose prose-lg max-w-none;
  @apply prose-headings:mb-[.7em] prose-headings:mt-[1.2em] prose-headings:font-bold prose-headings:text-dark dark:prose-headings:text-white;
  @apply prose-h1:text-h1-sm md:prose-h1:text-h1;
  @apply prose-h2:text-h2-sm md:prose-h2:text-h2 prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2 dark:prose-h2:border-gray-700;
  @apply prose-h3:text-h3-sm md:prose-h3:text-h3;
  @apply prose-hr:border-border prose-hr:dark:border-darkmode-border;
  @apply prose-p:text-base prose-p:leading-relaxed;
  @apply prose-blockquote:rounded-lg prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:bg-theme-light prose-blockquote:px-6 prose-blockquote:py-3 prose-blockquote:shadow-md prose-blockquote:dark:bg-darkmode-theme-dark;
  @apply prose-code:px-2 prose-code:py-0.5 prose-code:rounded prose-code:bg-gray-100 prose-code:text-primary dark:prose-code:bg-gray-800 dark:prose-code:text-darkmode-primary;
  @apply prose-pre:rounded-lg prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800;
  @apply prose-strong:text-dark prose-strong:dark:text-light;
  @apply prose-a:text-primary prose-a:font-medium prose-a:no-underline hover:prose-a:underline dark:prose-a:text-darkmode-primary;
  @apply prose-li:text-text prose-li:dark:text-light prose-li:my-1;
  @apply prose-table:overflow-hidden prose-table:border prose-table:border-border prose-table:rounded-lg prose-table:shadow-sm prose-table:dark:border-darkmode-border;
  @apply prose-thead:border-border prose-thead:bg-theme-light prose-th:px-4 prose-th:py-4 prose-th:text-dark prose-thead:dark:border-darkmode-border prose-thead:dark:bg-darkmode-theme-dark prose-th:dark:text-light;
  @apply prose-tr:border-border prose-tr:dark:border-darkmode-border;
  @apply prose-td:px-4 prose-td:py-4 prose-td:dark:text-light;
  @apply prose-img:my-6 prose-img:rounded-lg prose-img:shadow-md;

  // Add styling for code blocks
  pre {
    @apply p-4 rounded-lg overflow-x-auto text-sm bg-gray-100 dark:bg-gray-800 my-6;
    code {
      @apply bg-transparent p-0 text-gray-800 dark:text-gray-200;
    }
  }

  // Add styling for figures and captions
  figure {
    @apply my-8;
    figcaption {
      @apply text-center text-sm text-gray-500 dark:text-gray-400 mt-2;
    }
  }

  // Add styling for lists
  ul, ol {
    @apply my-6 pl-6;
    li {
      @apply mb-2;
      &::marker {
        @apply text-primary dark:text-darkmode-primary;
      }
    }
  }
}


.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

//.swiper-slide img {
//  display: block;
//  width: 100%;
//  height: 100%;
//  object-fit: cover;
//}
// show by change the opacity-0 to opacity-100

.mega-menu {
  @apply opacity-0 rounded-2xl p-8 pt-3 transition-all shadow-xl ease-in-out duration-200  left-0 absolute text-left w-full mt-[12px] pointer-events-none max-h-[420px] backdrop-blur-2xl z-50 mb-16 sm:mb-0 bg-blue-950/20 ;
}
.hoverable {
  @apply static;
  &:hover .mega-menu {
    @apply opacity-100 pointer-events-auto block;
  }
}


.floating {
  animation-name: floating;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}
@keyframes floating {
  0% { transform: translate(0, 0px); }
  50% { transform: translate(0, 8px); }
  100% { transform: translate(0, -0px); }
}
.floating-4 {
  animation-name: floating;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}
@keyframes floating-4 {
  0% { transform: translate(0, 0px); }
  50% { transform: translate(0, 8px); }
  100% { transform: translate(0, -0px); }
}



.bottom-card-frame {
  padding: 0px 50px 50px 50px;
  border-radius: 0px 0px 50px 50px;
  border-bottom: 2px solid rgba(79, 70, 229, 0.20);
  background: linear-gradient(180deg, #FFF 50%, #E8E7FC 100%);
  position: relative;
}
.bottom-card-frame:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0px 50px 50px 50px;
  padding: 2px;
  background: linear-gradient(180deg, #FFFFFF 0%, #E8E7FC 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  transition: all .6s ease-in-out;
  width: 100%;
  height: 100%;
}
.bottom-card {
  border-radius: 0px 0px 25px 25px;
  background: linear-gradient(180deg, rgba(221, 219, 255, 0.00) 0%, #DDDBFF 100%);
  position: relative;
}
.wrapper-card {
  padding: 50px;
  transition: all .6s ease-in-out;
}
.bottom-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0px 0px 25px 25px;
  padding: 2px;
  background: linear-gradient(0deg, #4F46E5 20.41%, #FFFFFF 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  transition: all .6s ease-in-out;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.feature-index {
  @apply text-[1em] text-dark dark:text-white;

}

.content figure iframe {
  @apply w-full h-auto;
}


// loader animate
.loader {
  z-index: 10;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 460px;
  height: 460px;
  background: transparent;
  border-radius: 50%;
  color: #B7E7F2;
}
.loader::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: animateC 40s linear infinite;
}
.loader span {
  display: block;
  position: absolute;
  top: calc(50% - 2px);
  left: 50%;
  width: 50%;
  height: 4px;
  background: transparent;
  transform-origin: left;
  animation: animate 40s linear infinite;
}
.loader span::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #B7E7F2;
  top: -6px;
  right: -8px;
  box-shadow: 0 0 20px 5px #B7E7F2;
}
@keyframes animateC {
  0% {transform: rotate(0deg);}
  100% {transform: rotate(360deg);}
}
@keyframes animate {
  0% {transform: rotate(45deg);}
  100% {transform: rotate(405deg);}
}

// Component styles
@layer components {
  // ... rest of your component styles ...
}

// Hide scrollbar utility class
.no-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* WebKit */
  width: 0;
  height: 0;
}

// Force width constraint for carousel
.force-width-constraint {
  width: 1200px !important;
  max-width: 1200px !important;
  min-width: 1200px !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}
