@use 'sass:map';
@use 'base' as *;

// navbar toggler
input#nav-toggle:checked ~ label#show-button {
  @apply hidden;
}

input#nav-toggle:checked ~ label#hide-button {
  @apply flex md:hidden;
}

input#nav-toggle:checked ~ #nav-menu {
  @apply block md:flex;
}

// header
.header {
  @apply py-2 !pt-0 lg:py-3 lg:pb-0 px-1 md:px-2 lg:px-14 top-0 z-40 w-full bg-white/90 dark:border-darkmode-border dark:bg-darkmode-body/80 sticky;
}

// navbar items
.navbar {
  @apply relative flex flex-wrap pb-[4px] items-center  px-1  sm:px-6 lg:justify-between;
}

a.download-btn {
  @apply ml-2 font-semibold text-base rounded-lg bg-primary text-white py-3 px-6;
  @apply transition-all duration-300 hover:bg-primary/90;
  @apply hover:scale-[1.02] active:scale-[0.98];
  @apply shadow-md hover:shadow-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50;
  outline: none;
  border: none;
}

.navbar-brand {
  @apply text-xl font-semibold text-dark;
  image {
    @apply max-h-full max-w-full;
  }
}

.navbar-nav {
  @apply text-center font-secondary md:text-left;
}

.nav-link {
  @apply relative px-[13px] py-2 text-[#52687B] transition hover:text-primary dark:text-white dark:hover:text-darkmode-primary font-medium;
  &.active {
    @apply text-blue-800;
  }
}

.nav-dropdown {
  @apply mr-0;
}

.nav-dropdown-list {
  @apply z-10 rounded-lg bg-white p-4 shadow transition;
}

.nav-dropdown-item {
  @apply mb-1;
}

.nav-dropdown-link {
  @apply min-w-[150px] py-1 text-lg font-semibold text-dark transition hover:text-primary dark:hover:text-darkmode-primary;
}

// Navigation styles
@layer components {
  // ... rest of your navigation styles ...
}
