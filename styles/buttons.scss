@use 'sass:map';
@use 'base' as *;

// Unified Button System
@layer components {
  .btn {
    @apply inline-flex items-center justify-center font-semibold transition-all duration-300 rounded-lg;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50;
    @apply hover:scale-[1.02] active:scale-[0.98];

    // Default size (medium)
    @apply px-6 py-3 text-base;

    &-primary {
      @apply text-white bg-primary hover:bg-primary/90;
    }

    &-secondary {
      @apply text-white bg-secondary hover:bg-secondary/90;
    }

    &-outline {
      @apply border-2 border-primary text-primary hover:bg-primary hover:text-white;
    }

    // Size variants
    &-sm {
      @apply px-4 py-2 text-sm;
    }

    &-md {
      @apply px-6 py-3 text-base;
    }

    &-lg {
      @apply px-8 py-4 text-lg;
    }

    // CTA Button variants
    &-cta {
      @apply text-white bg-primary hover:bg-primary/90;
      @apply shadow-md hover:shadow-lg;
    }

    &-cta-outline {
      @apply border-2 border-primary text-primary hover:bg-primary hover:text-white;
      @apply shadow-sm hover:shadow-md;
    }
  }
}

.btn-badge {
  @apply ml-2 rounded bg-[#dce9e9] px-[.3rem] py-[.15rem] text-xs dark:bg-darkmode-theme-light;
}

.btn-outline-primary {
  @apply border border-primary/25 bg-transparent from-primary to-secondary hover:bg-gradient-to-r hover:text-white;
  &:hover .btn-badge {
    @apply border-primary bg-primary text-white;
  }
}

.btn-demo {
  @apply border border-primary/25 from-primary to-secondary text-primary hover:bg-gradient-to-r hover:text-white dark:text-darkmode-primary;
  @apply dark:text-white;
}

.btn-download {
  @apply border border-primary/25 bg-primary/[.1] from-primary to-secondary text-primary hover:bg-gradient-to-r hover:text-white dark:text-darkmode-primary;
  @apply dark:bg-dark/[.1] dark:text-white;
}

.btn-github {
  @apply border border-[#151b22] bg-[#151b22] text-white dark:bg-darkmode-theme-light;
}

.btn-loading {
  @apply relative pr-12;
  &::after {
    @apply absolute right-[1em] top-2.5 h-[1.5em] w-[1.5em] rounded-full border-[4px] content-[""];
    border-color: #fff transparent #fff transparent;
    animation: loading 1.2s infinite;
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
