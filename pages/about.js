import Base from "@/layouts/Baseof";
import UserRating from "@/layouts/partials/UserRating";
import Team from '@/layouts/partials/Team';
import { useTranslation } from "@/lib/utils/i18n";
import Image from "next/image";
import LanguageSwitcher from "@/layouts/components/LanguageSwitcher";
import dynamic from "next/dynamic";


const FinalCTASection = dynamic(() => import('../layouts/fluxbuilder/FinalCTASection'), {
  loading: () => <div className="min-h-[200px] flex items-center justify-center"><div className="w-8 h-8 border-4 rounded-full border-primary border-t-transparent animate-spin"></div></div>
});

const About = () => {
  const { t } = useTranslation();
  const meta_title = t('about.meta_title', 'About Us');
  const description = t('about.meta_description', 'Learn about InspireUI, a design-first platform empowering anyone to create exceptional mobile apps without coding.');

  return (
    <Base title={t('about.title', 'About Us')} canonical="/about" meta_title={meta_title} description={description}>

      <div className="mx-0 max-w-[100%] mt-2 md:mt-4 pt-4 md:pt-6 pb-2 md:pb-4 mb-2 container-home home-hero hero">
        <h1 className="max-w-[700px] m-auto py-2 md:py-4 text-center text-2xl md:text-3xl">{t('about.heading', 'About Us')}</h1>
      </div>

      <section className="py-8 md:py-16 lg:py-12">
        <div className="pt-6 md:pt-10 pb-6 md:pb-10 overflow-hidden bg-white">
          <div className="px-4 md:px-6 mx-auto max-w-7xl lg:px-8">
            <div className="grid max-w-2xl grid-cols-1 mx-auto gap-x-8 md:gap-x-16 gap-y-12 md:gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
              <div className="items-start justify-start lg:mr-0 lg:max-w-lg">
                <h2 className="mt-2 text-2xl md:text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  {t('about.build_title', 'Build Without Limits')}
                </h2>
                <p className="md:mt-4 text-base md:text-[1.2em] leading-7 md:leading-8 text-gray-900">
                  {t('about.build_description1', 'InspireUI is a design-first platform that empowers anyone to create exceptional mobile apps without writing a single line of code.')}
                </p>

                <p className="mt-4 text-base md:text-[1.2em] text-gray-900">
                  {t('about.build_description2', 'Our mission is to make app development accessible and enjoyable for everyone, providing the tools and resources to turn ideas into reality.')}
                </p>
              </div>

              <div className="flex items-start">
                <div className="relative grid grid-cols-2 gap-4 md:gap-6 mt-6 md:mt-10 md:mt-0">
                  <div className="overflow-hidden aspect-w-3 aspect-h-4">
                    <Image 
                      className="object-cover object-top origin-top scale-150" 
                      src="/images/users/team-work.jpg" 
                      alt={t('about.alt_team_work', 'Team collaboration at InspireUI')} 
                      width={500}
                      height={600}
                      loading="eager"
                      priority={true}
                    />
                  </div>

                  <div className="relative">
                    <div className="h-full overflow-hidden aspect-w-3 aspect-h-4">
                      <Image 
                        className="object-cover scale-150 lg:origin-bottom-right"
                        src="/images/users/woman-working-on-laptop.jpg" 
                        alt={t('about.alt_woman_working', 'Woman developing mobile app with InspireUI')} 
                        width={500}
                        height={600}
                        loading="eager"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <section className="py-8 md:py-10 bg-gray-50 sm:py-12 md:py-16 lg:py-20 md:py-24">
          <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div className="grid items-center grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 xl:grid-cols-6 sm:gap-x-8 md:gap-x-12 gap-y-8 md:gap-y-12">
              <div className="lg:col-span-2">
                <h2 className="mt-2 text-2xl md:text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  {t('about.stats_heading1', '1 team.')}<br/>
                  {t('about.stats_heading2', '5+ years.')}<br/>
                  {t('about.stats_heading3', '26K+ licenses.')}<br/>
                </h2>
                <p className="mt-4 md:mt-6 items-start text-base md:text-[1.2em] text-gray-900 justify-start mx-auto lg:mr-0 lg:max-w-lg">
                  {t('about.stats_description', 'Leading the mobile app market since 2018, InspireUI offers the highest quality templates and has generated over $1.5 million in revenue.')}
                </p>
              </div>

              <div className="lg:col-span-3 xl:col-span-4">
                <div className="grid items-center max-w-4xl grid-cols-2 mx-auto lg:grid-cols-4 gap-x-6 md:gap-x-8 gap-y-8 md:gap-y-14">
                  <div>
                    <Image className="object-contain w-full h-3 md:h-4 mx-auto" src="/images/users/logo-1.png" alt={t('about.client_logo', 'Client logo')} width={150} height={40} />
                  </div>

                  <div>
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-2.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div>
                    <Image className="object-contain w-full h-5 mx-auto" src="/images/users/logo-3.png" alt={t('about.client_logo', 'Client logo')} width={150} height={50} />
                  </div>

                  <div>
                    <Image className="object-contain w-full h-5 mx-auto" src="/images/users/logo-4.png" alt={t('about.client_logo', 'Client logo')} width={150} height={50} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-5.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-6.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-7.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-8.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-9.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-5 mx-auto" src="/images/users/logo-10.png" alt={t('about.client_logo', 'Client logo')} width={150} height={50} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-11.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>

                  <div className="hidden lg:block">
                    <Image className="object-contain w-full h-6 mx-auto" src="/images/users/logo-12.png" alt={t('about.client_logo', 'Client logo')} width={150} height={60} />
                  </div>
                </div>

                <div className="hidden">
                  <div className="w-2 h-2 md:w-2.5 md:h-2.5 rounded-full bg-blue-600 block"></div>
                  <div className="w-2 h-2 md:w-2.5 md:h-2.5 rounded-full bg-gray-300 block"></div>
                  <div className="w-2 h-2 md:w-2.5 md:h-2.5 rounded-full bg-gray-300 block"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <Team/>

        <section className="overflow-hidden">
          <div className="relative pt-8 md:pt-12 bg-gray-50 sm:pt-12 md:pt-16 lg:pt-20 md:pt-24 xl:pt-32 md:pt-40">
            <div className="flex flex-col">
              <div className="block lg:hidden">
                <Image 
                  className="w-full max-w-lg mx-auto" 
                  src="/images/users/man.png" 
                  alt={t('about.testimonial_person', 'Shane Lewis, customer from Canada')} 
                  width={400}
                  height={600}
                />
              </div>

              <div className="py-6 md:py-8 bg-gray-700 lg:order-2 sm:py-8 md:py-12">
                <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                  <div className="flex flex-col items-center sm:justify-center sm:flex-row lg:justify-start">
                    <a className="text-white/70 text-sm md:text-[0.9em]"
                       href='https://www.trustpilot.com/reviews/661bee0d26838d119a1ba1b0'>
                      <Image className="w-auto h-5 md:h-6 mr-2" src="/images/users/stars-5.svg" alt={t('about.five_stars', '5 star rating')} width={120} height={24} />
                      #Trustpilot
                    </a>
                    <div className="mt-3 md:mt-5 sm:ml-8 md:ml-12 sm:mt-0">
                      <p className="text-lg md:text-xl font-bold text-white font-pj">{t('about.testimonial_name', 'Shane Lewis')}</p>
                      <p className="text-xs md:text-sm font-normal font-pj text-white mt-1 md:mt-1.5">{t('about.testimonial_role', 'Customer from Canada')}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div className="flex items-end lg:order-1">
                  <div className="py-8 md:py-12 lg:pt-0 lg:pb-24 lg:w-1/2">
                    <blockquote className="text-center lg:text-left">
                      <p className="text-xl md:text-2xl lg:text-3xl font-normal leading-relaxed text-gray-900 font-pj">
                        {t('about.testimonial_quote', '"Trust Inspire UI! I\'ve been building websites for over 20 years but needed to create a mobile app and came across Inspire UI. Using the software is relatively simple, and they continue to make it even more easy to use with recent updates..."')}
                      </p>
                    </blockquote>
                  </div>

                  <div className="absolute bottom-0 right-0 hidden w-1/2 lg:block">
                    <Image 
                      className="w-full max-w-lg ml-10 mr-auto" 
                      src="/images/users/man.png" 
                      alt={t('about.testimonial_person', 'Shane Lewis, customer from Canada')} 
                      width={500}
                      height={700}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <UserRating/>

        <section className="py-8 md:py-12 bg-gray-50 sm:py-12 md:py-16 lg:py-16 md:py-20 xl:py-20 md:py-24">
          <div className="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
            <h2 className="mt-2 text-2xl md:text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {t('about.contact_heading', 'Let\'s Build Something Amazing Together')}
            </h2>

            <div className="grid grid-cols-1 mt-6 md:mt-8 lg:grid-cols-2 lg:mt-10 md:mt-12 gap-y-8 md:gap-y-12 sm:gap-y-12 md:gap-y-16 lg:gap-x-12 md:gap-x-16 xl:gap-x-20 md:gap-x-24">
              <div className="pl-4 md:pl-6 sm:pl-6 md:pl-8">
                <div className="relative h-full">
                  <div className="flex-none max-w-3xl sm:max-w-5xl lg:max-w-none">
                    <div data-aos="fade-up" data-aos-delay="100" data-aos-once="true"
                         className="absolute top-[2rem] md:top-[4rem] left-[2rem] md:left-[4rem] floating text-2xl md:text-4xl">
                      <Image 
                        alt={t('about.alt_sphere', 'Decorative sphere element')} 
                        loading="lazy"
                        width={20}
                        height={20}
                        className="max-w-2xl"
                        src="/images/home/<USER>"
                      />
                    </div>
                    <div className="absolute z-10 loader">
                      <span></span>
                    </div>
                    <div data-aos="fade-up" data-aos-delay="400" data-aos-once="true"
                         className="absolute bottom-[3rem] md:bottom-[6rem] right-[3rem] md:right-[5rem] floating text-2xl md:text-4xl">
                      <Image 
                        alt={t('about.alt_sphere', 'Decorative sphere element')} 
                        loading="lazy"
                        width={28}
                        height={28}
                        className="max-w-2xl"
                        src="/images/home/<USER>"
                      />
                    </div>
                    <Image 
                      alt={t('about.alt_app_screenshot', 'Mobile app created with InspireUI')} 
                      loading="lazy"
                      width={2432}
                      height={1442} 
                      className="w-full"
                      src="/images/home/<USER>"
                    />
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm md:text-base font-normal leading-6 md:leading-7 text-gray-600 lg:text-lg lg:leading-8">
                  {t('about.contact_description', 'Have a question, need support, or want to discuss your next big app idea? We\'re here to help! Our team is dedicated to providing exceptional customer service.')}
                </p>

                <div className="grid grid-cols-1 mt-8 md:mt-12 sm:grid-cols-2 gap-y-6 md:gap-y-8 sm:gap-8 md:gap-12 xl:gap-x-12 md:gap-x-16 sm:mt-12 md:mt-16 lg:mt-16 md:mt-20">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 md:w-6 md:h-6 text-blue-600 shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <div className="ml-3 md:ml-4">
                      <h3 className="text-base md:text-lg font-medium text-gray-900">{t('about.office_hours_title', 'Office Hours')}</h3>
                      <p className="mt-2 md:mt-4 text-xs md:text-sm font-normal text-gray-600">
                        {t('about.office_hours_weekdays', 'Monday-Friday')}<br/>
                        {t('about.office_hours_time', '8:00 am to 5:00 pm')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <svg className="w-5 h-5 md:w-6 md:h-6 text-blue-600 shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    <div className="ml-3 md:ml-4">
                      <h3 className="text-base md:text-lg font-medium text-gray-900">{t('about.address_title', 'Our Address')}</h3>
                      <p className="mt-2 md:mt-4 text-xs md:text-sm font-normal text-gray-600">{t('about.address_line1', 'SBI Building, Street 3,')}<br/>{t('about.address_line2', 'Quang Trung Software Park, Vietnam')}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <svg className="w-5 h-5 md:w-6 md:h-6 text-blue-600 shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round"
                            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    <div className="ml-3 md:ml-4">
                      <h3 className="text-base md:text-lg font-medium text-gray-900">{t('about.follow_us_title', 'Follow us')}</h3>
                      <p className="mt-2 md:mt-4 text-xs md:text-sm font-normal text-gray-600">
                        <a href="https://x.com/inspireui" target="blank">{t('about.twitter', 'Twitter')}</a>
                        <br />
                        <a className="inline-block my-1 md:my-2" href="https://youtube.com/inspireui?sub_confirmation=1" target="blank">{t('about.youtube', 'Youtube channel')}</a>
                        <br />
                        <a href="https://www.facebook.com/groups/fluxbuilder" target="blank">{t('about.facebook', 'Facebook Group')}</a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <svg className="w-5 h-5 md:w-6 md:h-6 text-blue-600 shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                    <div className="ml-3 md:ml-4">
                      <h3 className="text-base md:text-lg font-medium text-gray-900">{t('about.contact_title', 'Get In Touch')}</h3>
                      <div className="mt-2 md:mt-4 text-xs md:text-sm font-normal text-gray-600">
                        {t('about.phone', 'Phone')}: +84-98990 8854<br/>
                        <span className="inline-block my-1 md:my-2">
                          {t('about.email', 'Email')}: <a href="mailto:<EMAIL>"><EMAIL></a>
                        </span> 
                        <br />
                        <a href="https://wa.me/849908854">{t('about.chat_with_us', 'Chat with us!')}</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </section>

      {/* Final CTA Section */}
      <FinalCTASection />
    </Base>
  );
};

export default About;
