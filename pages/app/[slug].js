import Base from "../../layouts/Baseof";
import { getSinglePage } from "../../lib/contentParser";
import { slugify } from "../../lib/utils/textConverter";
import { dateFormat } from "../../lib/utils/dateFormat";
import ImageFallback from "../../layouts/components/ImageFallback";
import { useState, useEffect, useRef } from "react";
import AppDialog from "../../layouts/components/AppDialog";
import Link from "next/link";
import { ArrowDownTrayIcon, StarIcon, ArrowPathIcon, DevicePhoneMobileIcon, ShoppingCartIcon, PlayIcon } from "@heroicons/react/24/outline";
import Head from "next/head";
import videoShowcase from "../../config/video-showcase.json";

// Custom styles for screenshot carousel
const screenshotCarouselStyles = `
  .screenshot-carousel-container {
    position: relative;
    overflow: hidden;
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }

  .screenshot-carousel {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    padding: 0.5rem 0;
    position: relative;
  }

  .screenshot-carousel::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Gradient overlays to indicate more content */
  .screenshot-carousel-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 60px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8));
    pointer-events: none;
    z-index: 1;
  }

  .dark .screenshot-carousel-container::after {
    background: linear-gradient(to right, rgba(30,30,30,0), rgba(30,30,30,0.8));
  }

  .screenshot-item {
    flex: 0 0 auto;
    width: 180px;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    position: relative;
  }

  .screenshot-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
  }

  @media (min-width: 768px) {
    .screenshot-item {
      width: 200px;
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .screenshot-item {
    animation: slideIn 0.5s ease forwards;
    animation-delay: calc(var(--index) * 0.1s);
    opacity: 0;
  }
`;

// Common shadow classes
const commonClasses = {
  card: "bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200/80 dark:border-neutral-700/50",
  cardShadow: "",
  hoverShadow: "hover:shadow-[0_12px_40px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_12px_40px_rgb(0,0,0,0.2)]",
};

const ShowcaseDetail = ({ frontmatter, content }) => {
  const [selectedApp, setSelectedApp] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const carouselRef = useRef(null);

  // Auto-scroll effect for the carousel
  useEffect(() => {
    const carousel = carouselRef.current;
    if (!carousel) return;

    let scrollInterval;

    // Start auto-scrolling when not paused
    if (!isPaused) {
      scrollInterval = setInterval(() => {
        carousel.scrollBy({
          left: 100,
          behavior: 'smooth'
        });

        // Reset to beginning when reaching the end
        const isAtEnd = carousel.scrollLeft + carousel.clientWidth >= carousel.scrollWidth - 50;
        if (isAtEnd) {
          carousel.scrollTo({
            left: 0,
            behavior: 'smooth'
          });
        }
      }, 3000);
    }

    // Cleanup interval on component unmount or when paused
    return () => {
      if (scrollInterval) clearInterval(scrollInterval);
    };
  }, [isPaused]);

  // Add safe defaults for required properties
  const cms = frontmatter?.cms || ['WooCommerce'];
  const categories = frontmatter?.category || [];
  const appName = frontmatter?.app_name || 'App';

  // Find video data for this app from video-showcase.json
  const videoData = videoShowcase.apps.find(
    app => app.bundle_id === frontmatter.bundleId
  );
  const youtubeId = videoData?.youtube_id;
  const embedUrl = youtubeId ? `https://www.youtube.com/embed/${youtubeId}` : null;

  // Combine iOS and Android screenshots
  const allScreenshots = [
    ...(frontmatter.iosScreen || []).map(img => ({
      width: 275,
      height: 490,
      src: img
    })),
    ...(frontmatter.androidScreens || []).map(img => ({
      width: 275,
      height: 490,
      src: img
    })),
    ...(frontmatter.ios_screenshots || []),
    ...(frontmatter.android_screenshots || [])
  ];

  // Format content by replacing newlines with <br> tags
  const formattedContent = content.split('\n').map((line, i) => (
    <span key={i}>
      {line}
      {i < content.split('\n').length - 1 && <br />}
    </span>
  ));

  const openAppDialog = () => {
    setSelectedApp({
      frontmatter: {
        ...frontmatter,
        iosScreen: allScreenshots.map(img => img.src),
        androidScreens: []
      }
    });
  };

  const closeAppDialog = () => {
    setSelectedApp(null);
  };

  const handleImageClick = () => {
    // Open the app dialog to view all screenshots
    openAppDialog();
  };

  // Get the first screenshot for Open Graph image
  const ogImage = allScreenshots[0]?.src || frontmatter.iosIcon || frontmatter.androidIcon;
  const ogDescription = content?.substring(0, 160) || `Check out ${appName} - A beautiful mobile app built with FluxBuilder`;

  return (
    <>
      <Head>
        <title>{`${appName} - FluxBuilder Showcase`}</title>
        <meta name="description" content={ogDescription} />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`${process.env.NEXT_PUBLIC_SITE_URL}/showcase/${frontmatter.slug}`} />
        <meta property="og:title" content={`${appName} - FluxBuilder Showcase`} />
        <meta property="og:description" content={ogDescription} />
        <meta property="og:image" content={ogImage} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content={`${appName} mobile app showcase`} />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={`${process.env.NEXT_PUBLIC_SITE_URL}/showcase/${frontmatter.slug}`} />
        <meta property="twitter:title" content={`${appName} - FluxBuilder Showcase`} />
        <meta property="twitter:description" content={ogDescription} />
        <meta property="twitter:image" content={ogImage} />
        <meta property="twitter:image:alt" content={`${appName} mobile app showcase`} />

        {/* Additional SEO tags */}
        <meta name="keywords" content={`${appName}, mobile app, ecommerce app, ${cms.join(', ')}, ${categories.join(', ')}`} />
        <meta name="author" content="FluxBuilder" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href={`${process.env.NEXT_PUBLIC_SITE_URL}/showcase/${frontmatter.slug}`} />
      </Head>

      <Base
        title={`${appName} - FluxBuilder Showcase`}
        meta_title={`${appName} - FluxBuilder Showcase`}
        description={ogDescription}
      >
        <style jsx global>{screenshotCarouselStyles}</style>
        {/* Hero Section */}
        <section className="pb-1 section bg-gradient-to-b from-slate-200 to-slate-100 dark:from-neutral-900 dark:to-neutral-800">
          <div className="container">
            <div className="row">
              <div className="mx-auto lg:col-10">
                <div className="flex flex-col items-stretch gap-8 lg:flex-row">
                  {/* App Info Card */}
                  <div className="lg:w-1/3">
                    <div className={`${commonClasses.card} ${commonClasses.cardShadow} relative overflow-hidden h-full flex flex-col p-8`}>
                      {/* Background Pattern */}
                      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-bl-[100px] -z-0" />

                      {/* App Icon */}
                      <div className="relative">
                        <div className="w-32 mx-auto mb-6 overflow-hidden shadow-lg aspect-square rounded-2xl ">
                          <ImageFallback
                            src={frontmatter.iosIcon || frontmatter.androidIcon}
                            alt={appName}
                            width={128}
                            height={128}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      </div>

                      {/* App Info */}
                      <div className="relative flex flex-col flex-1 text-center">
                        <div>
                          <h1 className="mb-3 text-2xl font-bold text-transparent bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-white dark:to-neutral-400 bg-clip-text drop-shadow-sm">
                            {appName}
                          </h1>

                          {/* Tags */}
                          <div className="flex flex-wrap justify-center gap-2 mb-6">
                            {cms[0] && (
                              <div className="tracking-wide font-semibold py-1.5 text-[0.8em] px-4 bg-blue-100/60 rounded-xl text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 shadow-[0_2px_8px_rgb(59,130,246,0.08)]">
                                {cms[0]}
                              </div>
                            )}
                            {categories.map((category, i) => (
                              <Link
                                key={i}
                                href={`/showcase?category=${slugify(category)}`}
                                className={`font-semibold py-1.5 text-[0.8em] px-4 bg-neutral-100 dark:bg-neutral-700/50 rounded-xl hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-all hover:shadow-[0_4px_12px_rgb(0,0,0,0.06)]`}
                              >
                                {category}
                              </Link>
                            ))}
                          </div>

                          {/* Last Update */}
                          <div className={`inline-flex items-center gap-1.5 px-3 py-1.5 bg-neutral-100/80 dark:bg-neutral-800/80 rounded-full text-[0.8em] text-neutral-600 dark:text-neutral-400 mb-6`}>
                            <ArrowPathIcon className="w-4 h-4" />
                            Updated {dateFormat(frontmatter.date)}
                          </div>
                        </div>


                        {/* Stats Cards */}
                        <div className="grid grid-cols-2 gap-4 place-items-center">
                          {frontmatter.ios && frontmatter.iosInstall > 0 && !frontmatter.android && (
                            <div className="col-span-2 bg-white dark:bg-neutral-800 p-5 rounded-xl relative overflow-hidden group hover:shadow-[0_12px_40px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_12px_40px_rgb(0,0,0,0.2)] transition-all border border-neutral-200/50 dark:border-neutral-700/50">
                              <div className="absolute inset-0 transition-opacity opacity-0 bg-gradient-to-br from-blue-500/5 to-transparent group-hover:opacity-100" />
                              <div className="relative">
                                <div className="flex items-center gap-2 mb-2 text-sm text-neutral-600 dark:text-neutral-400">
                                  <StarIcon className="w-4 h-4 text-yellow-500 drop-shadow-sm" />
                                  <span>App Store</span>
                                </div>
                                <div className="text-2xl font-bold drop-shadow-sm">{frontmatter.iosReview}</div>
                                <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">Reviews</div>
                              </div>
                            </div>
                          )}
                          {!frontmatter.ios && frontmatter.android && (
                            <div className="col-span-2 bg-white dark:bg-neutral-800 p-5 rounded-xl relative overflow-hidden group hover:shadow-[0_12px_40px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_12px_40px_rgb(0,0,0,0.2)] transition-all border border-neutral-200/50 dark:border-neutral-700/50">
                              <div className="absolute inset-0 transition-opacity opacity-0 bg-gradient-to-br from-green-500/5 to-transparent group-hover:opacity-100" />
                              <div className="relative">
                                <div className="flex items-center gap-2 mb-2 text-sm text-neutral-600 dark:text-neutral-400">
                                  <DevicePhoneMobileIcon className="w-4 h-4 text-green-500 drop-shadow-sm" />
                                  <span>Google Play</span>
                                </div>
                                <div className="text-2xl font-bold drop-shadow-sm">
                                  {frontmatter.androidInstall.replace(/\+/g, '')}
                                </div>
                                <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">Installs</div>
                              </div>
                            </div>
                          )}
                          {frontmatter.ios && frontmatter.iosInstall > 0 && frontmatter.android && (
                            <>
                              <div className="bg-white dark:bg-neutral-800 p-5 rounded-xl relative overflow-hidden group hover:shadow-[0_12px_40px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_12px_40px_rgb(0,0,0,0.2)] transition-all border border-neutral-200/50 dark:border-neutral-700/50">
                                <div className="absolute inset-0 transition-opacity opacity-0 bg-gradient-to-br from-blue-500/5 to-transparent group-hover:opacity-100" />
                                <div className="relative">
                                  <div className="flex items-center gap-2 mb-2 text-sm text-neutral-600 dark:text-neutral-400">
                                    <StarIcon className="w-4 h-4 text-yellow-500 drop-shadow-sm" />
                                    <span>App Store</span>
                                  </div>
                                  <div className="text-2xl font-bold drop-shadow-sm">{frontmatter.iosReview}</div>
                                  <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">Reviews</div>
                                </div>
                              </div>
                              <div className="bg-white dark:bg-neutral-800 p-5 rounded-xl relative overflow-hidden group hover:shadow-[0_12px_40px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_12px_40px_rgb(0,0,0,0.2)] transition-all border border-neutral-200/50 dark:border-neutral-700/50">
                                <div className="absolute inset-0 transition-opacity opacity-0 bg-gradient-to-br from-green-500/5 to-transparent group-hover:opacity-100" />
                                <div className="relative">
                                  <div className="flex items-center gap-2 mb-2 text-sm text-neutral-600 dark:text-neutral-400">
                                    <DevicePhoneMobileIcon className="w-4 h-4 text-green-500 drop-shadow-sm" />
                                    <span>Google Play</span>
                                  </div>
                                  <div className="text-2xl font-bold drop-shadow-sm">
                                    {frontmatter.androidInstall.replace(/\+/g, '')}
                                  </div>
                                  <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">Installs</div>
                                </div>
                              </div>
                            </>
                          )}
                        </div>

                        <div className="flex-1" />


                        {/* Store Links */}
                        <div className="flex flex-row justify-center gap-4 mt-4">
                          {frontmatter.ios && (
                            <a
                              href={frontmatter.ios}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="ios-btn transition-all hover:scale-[1.02] active:scale-[0.98] hover:shadow-lg flex-1"
                            >
                              <img
                                width={140}
                                className="mx-auto drop-shadow-sm"
                                src="/images/appstore.svg"
                                alt={`Download ${appName} on App Store`}
                              />
                            </a>
                          )}
                          {frontmatter.android && (
                            <a
                              href={frontmatter.android}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="android-btn transition-all active:scale-[0.98] hover:scale-[1.1] flex-1"
                            >
                              <img
                                width={140}
                                className="mx-auto drop-shadow-sm"
                                src="/images/google-play.svg"
                                alt={`Download ${appName} on Google Play`}
                              />
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Screenshots Section */}
                  <div className="lg:w-2/3">
                    <div className={`${commonClasses.card} ${commonClasses.cardShadow} p-8 h-full`}>
                      <div className="flex items-center justify-between mb-8">
                        <h2 className="text-xl font-bold text-transparent bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-white dark:to-neutral-400 bg-clip-text drop-shadow-sm">
                          App Screenshots
                        </h2>
                        <button
                          onClick={openAppDialog}
                          className={`inline-flex rounded-3xl items-center gap-2 px-4 py-2 text-sm text-primary bg-neutral-100 dark:bg-neutral-700/50`}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                          </svg>
                          <span>View All</span>
                        </button>
                      </div>
                      <div className="screenshot-carousel-container">
                        <div
                          ref={carouselRef}
                          className="screenshot-carousel"
                          onMouseEnter={() => setIsPaused(true)}
                          onMouseLeave={() => setIsPaused(false)}
                        >
                          {allScreenshots.slice(0, 10).map((screenshot, index) => (
                            <div
                              key={index}
                              className="screenshot-item"
                              style={{ '--index': index }}
                              onClick={() => handleImageClick()}
                            >
                              <div className="relative aspect-[9/16] rounded-lg overflow-hidden">
                                <ImageFallback
                                  src={screenshot.src}
                                  fill
                                  className="object-cover"
                                  alt={`${appName} screenshot ${index + 1}`}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Navigation Controls */}
                      <div className="flex items-center justify-center gap-4 mt-4">
                        <button
                          onClick={() => carouselRef.current?.scrollBy({left: -400, behavior: 'smooth'})}
                          className="p-2 transition-all bg-white rounded-full shadow-md dark:bg-neutral-700 hover:shadow-lg"
                          aria-label="Scroll left"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>

                        {/* Auto-scroll indicator */}
                        <div className="flex items-center gap-1.5">
                          <div className={`h-1.5 w-1.5 rounded-full ${isPaused ? 'bg-neutral-300 dark:bg-neutral-600' : 'bg-primary animate-pulse'}`}></div>
                          <span className="text-xs text-neutral-500 dark:text-neutral-400">
                            {isPaused ? 'Paused' : 'Auto-scrolling'}
                          </span>
                        </div>

                        <button
                          onClick={() => carouselRef.current?.scrollBy({left: 400, behavior: 'smooth'})}
                          className="p-2 transition-all bg-white rounded-full shadow-md dark:bg-neutral-700 hover:shadow-lg"
                          aria-label="Scroll right"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="pt-10 section bg-slate-100">
          <div className="container">
            <div className="row">
              <div className="mx-auto lg:col-10">
                <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                  {/* Left Column - App Description */}
                  <div className="lg:col-span-2">
                    <div className={`${commonClasses.card} ${commonClasses.cardShadow} p-8 h-full`}>
                      <h2 className="mb-6 text-xl font-bold text-transparent bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-white dark:to-neutral-400 bg-clip-text drop-shadow-sm">About This App</h2>

                      {/* YouTube Video */}
                      {embedUrl && (
                        <div className="mb-8">
                          <div className="relative w-full overflow-hidden shadow-lg aspect-video rounded-xl">
                            <iframe
                              src={embedUrl}
                              title={`${appName} Demo Video`}
                              className="absolute inset-0 w-full h-full"
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                            />
                          </div>
                        </div>
                      )}

                      <div className="prose content dark:prose-invert max-w-none">
                        {formattedContent}
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Features & CTA */}
                  <div className="space-y-8">
                    {/* Key Features */}
                    <div className={`${commonClasses.card} ${commonClasses.cardShadow} p-8`}>
                      <h2 className="mb-6 text-xl font-bold text-transparent bg-gradient-to-r from-neutral-900 to-neutral-600 dark:from-white dark:to-neutral-400 bg-clip-text drop-shadow-sm">Key Features</h2>
                      <ul className="space-y-4">
                        <li className="flex items-start gap-3 p-3 transition-colors rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-700/50">
                          <ShoppingCartIcon className="w-5 h-5 text-primary mt-0.5 drop-shadow-sm" />
                          <span>Full-featured e-commerce solution</span>
                        </li>
                        <li className="flex items-start gap-3 p-3 transition-colors rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-700/50">
                          <DevicePhoneMobileIcon className="w-5 h-5 text-primary mt-0.5 drop-shadow-sm" />
                          <span>Native mobile app experience</span>
                        </li>
                        <li className="flex items-start gap-3 p-3 transition-colors rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-700/50">
                          <ArrowPathIcon className="w-5 h-5 text-primary mt-0.5 drop-shadow-sm" />
                          <span>Regular updates and improvements</span>
                        </li>
                      </ul>
                    </div>



                    {/* Free Download CTA */}
                    <div className={`bg-gradient-to-br from-primary to-primary/80 rounded-2xl p-8 ${commonClasses.cardShadow} ${commonClasses.hoverShadow} relative overflow-hidden group transition-all`}>
                      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_107%,rgba(255,255,255,0.08)_0%,rgba(255,255,255,0.06)_5%,rgba(255,255,255,0.04)_45%,rgba(255,255,255,0.02)_60%,transparent_90%)] opacity-0 group-hover:opacity-100 transition-opacity" />
                      <div className="relative">
                        <h3 className="mb-3 text-xl font-bold text-white drop-shadow-sm">Create Your Own App</h3>
                        <p className="mb-6 text-white/90">
                          Let FluxBuilder help you bring your creative ideas to life. Sign up today and start creating beautiful app.
                        </p>
                        <a
                          href="https://fluxbuilder.com/download"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 px-6 py-3 bg-white text-dark rounded-lg hover:bg-white/90 transition-colors w-full justify-center font-semibold shadow-[0_4px_12px_rgb(0,0,0,0.1)] hover:shadow-[0_8px_20px_rgb(0,0,0,0.15)]"
                        >
                          Get Free Download
                        </a>
                      </div>
                    </div>

                     {/* Lifetime Deal CTA */}
                     <div className={`bg-neutral-900 dark:bg-black rounded-2xl p-8 ${commonClasses.cardShadow} ${commonClasses.hoverShadow} relative overflow-hidden group transition-all`}>
                      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_107%,rgba(255,255,255,0.08)_0%,rgba(255,255,255,0.06)_5%,rgba(255,255,255,0.04)_45%,rgba(255,255,255,0.02)_60%,transparent_90%)] opacity-0 group-hover:opacity-100 transition-opacity" />
                      <div className="relative">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="px-2 py-1 text-xs font-medium text-orange-500 rounded-full bg-orange-300/20">Limited Time Offer</span>
                        </div>
                        <h3 className="mb-2 text-xl font-bold text-white drop-shadow-sm">Save up to 80% on FluxBuilder</h3>
                        <p className="mb-6 text-sm text-white/80">
                          Get lifetime access to FluxBuilder and create unlimited beautiful apps. One-time payment, lifetime updates.
                        </p>
                        <a
                          href="/lifetime"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 px-6 py-3 bg-white text-dark rounded-lg hover:bg-slate/90 transition-colors w-full justify-center font-semibold shadow-[0_4px_12px_rgb(0,0,0,0.1)] hover:shadow-[0_8px_20px_rgb(0,0,0,0.15)]"
                        >
                          Get Lifetime Deal
                        </a>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* App Dialog */}
        <AppDialog
          isOpen={!!selectedApp}
          onClose={closeAppDialog}
          app={selectedApp}
        />
      </Base>
    </>
  );
};

export default ShowcaseDetail;

// Generate static paths for all showcase items
export const getStaticPaths = async () => {
  const showcaseExamples = getSinglePage("content/showcase") || [];
  const regularExamples = getSinglePage("content/examples") || [];

  // Create a Map to store unique examples by bundleId
  const uniqueExamples = new Map();

  // Process showcase examples first (they take precedence)
  showcaseExamples.forEach(example => {
    if (example?.frontmatter?.bundleId) {
      uniqueExamples.set(example.frontmatter.bundleId, {
        ...example,
        frontmatter: {
          ...example.frontmatter,
          cms: example.frontmatter?.cms || ['WooCommerce'],
          category: example.frontmatter?.category || [],
        }
      });
    }
  });

  // Add regular examples only if they don't already exist
  regularExamples.forEach(example => {
    if (example?.frontmatter?.bundleId && !uniqueExamples.has(example.frontmatter.bundleId)) {
      uniqueExamples.set(example.frontmatter.bundleId, {
        ...example,
        frontmatter: {
          ...example.frontmatter,
          cms: example.frontmatter?.cms || ['WooCommerce'],
          category: example.frontmatter?.category || [],
        }
      });
    }
  });

  const paths = Array.from(uniqueExamples.values()).map((example) => ({
    params: {
      slug: example.slug,
    },
  }));

  return {
    paths,
    fallback: false,
  };
};

// Get static props for each showcase item
export const getStaticProps = async ({ params }) => {
  const { slug } = params;
  const showcaseExamples = getSinglePage("content/showcase") || [];
  const regularExamples = getSinglePage("content/examples") || [];

  // Find the example with matching slug
  const example = [...showcaseExamples, ...regularExamples].find(
    (example) => example.slug === slug
  );

  if (!example) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      frontmatter: example.frontmatter,
      content: example.content,
    },
  };
};