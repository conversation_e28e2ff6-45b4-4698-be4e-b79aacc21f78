import React, { useState } from 'react';
import Base from '@/layouts/Baseof';
import Link from 'next/link';
import Image from 'next/image';
import fs from 'fs';
import path from 'path';

// Simple Image Fallback component
const ImageFallback = ({ src, alt, ...props }) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    setHasError(true);
    setImgSrc('/images/features/default.png'); // Fallback image
  };

  if (hasError && !imgSrc.includes('default.png')) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-gray-100 rounded-lg">
        <div className="text-center text-gray-400">
          <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-sm">Image not available</p>
        </div>
      </div>
    );
  }

  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      onError={handleError}
    />
  );
};

const FeatureDetailPage = ({ feature, notFound }) => {
  // State for video/image display
  const [showVideo, setShowVideo] = useState(false);

  // Helper function to get YouTube thumbnail URL
  const getYouTubeThumbnail = (youtubeId) => {
    return `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`;
  };

  // If notFound is true, render a not found message
  if (notFound) {
    return (
      <Base
        title="Feature Not Found | FluxBuilder - App Builder Without Code"
        meta_title="Feature Not Found | FluxBuilder - Professional Mobile App Builder"
        description="The requested feature could not be found."
      >
        <section className="relative py-20 overflow-hidden bg-gradient-to-b from-blue-50 via-white to-slate-50">
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div className="absolute rounded-full w-96 h-96 top-20 -left-48 bg-blue-200/20 blur-3xl"></div>
            <div className="absolute rounded-full w-96 h-96 bottom-20 -right-48 bg-blue-300/10 blur-3xl"></div>
          </div>

          <div className="container relative px-4 mx-auto">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-5xl">
                Feature Not Found
              </h1>
              <p className="mb-8 text-xl text-gray-600">
                The feature you're looking for could not be found.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link
                  href="/features"
                  className="inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white transition-all duration-300 bg-blue-600 rounded-lg shadow-md hover:shadow-lg hover:bg-blue-700"
                >
                  View All Features
                </Link>
              </div>
            </div>
          </div>
        </section>
      </Base>
    );
  }

  return (
    <Base
      title={`${feature.title} | FluxBuilder - App Builder Without Code`}
      meta_title={`${feature.title} | FluxBuilder - Professional Mobile App Builder`}
      description={feature.description}
    >
      {/* Hero Section - Improved contrast */}
      <section className="relative bg-gradient-to-b from-slate-50 to-white">
        <div className="mx-0 max-w[100%] pt-6 sm:pt-8 md:pt-12 pb-1 container-home home-hero hero4 text-center">
          <div className="mb-2 sm:mb-4">
            <div className="max-w-5xl mx-auto mt-6 sm:mt-8 md:mt-12 animate-fadeIn">
              <div className="mb-6 text-center sm:mb-8 md:mb-12">
                {/* Feature Title with Gradient */}
                <div className="flex items-center justify-center gap-3 mb-4">
                  <h1 className="mt-20 mb-3 text-3xl font-light text-transparent sm:mb-4 sm:text-4xl md:text-5xl lg:text-6xl bg-gradient-to-r from-primary/90 to-primary bg-clip-text">
                    {feature.title}
                  </h1>
                  <div className="flex items-center gap-2 mt-16">
                    {feature.youtubeId && (
                      <div className="w-6 h-6 flex items-center justify-center rounded-full bg-red-100">
                        <svg className="w-4 h-4 text-red-600" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    )}
                    {feature.isNew === true && (
                      <span className="inline-flex items-center px-3 py-1 text-xs font-medium tracking-wide text-emerald-700 bg-emerald-50 rounded-full border border-emerald-100">
                        NEW
                      </span>
                    )}
                    {feature.isHot === true && (
                      <span className="inline-flex items-center px-3 py-1 text-xs font-medium tracking-wide text-orange-700 bg-orange-50 rounded-full border border-orange-100">
                        HOT
                      </span>
                    )}
                  </div>
                </div>

                {/* Feature Description */}
                <p className="max-w-[700px] m-auto text-sm sm:text-base md:text-lg text-neutral-600 dark:text-neutral-400 px-4 sm:px-0 mb-8">
                  {feature.description}
                </p>

                {/* Action Buttons */}
                <div className="flex flex-wrap justify-center gap-4">
                  <Link
                    href="/download"
                    className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white transition-all duration-300 bg-blue-600 rounded-lg hover:bg-blue-700"
                  >
                    Try for Free
                  </Link>
                  <Link
                    href="/features"
                    className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-gray-700 transition-all duration-300 bg-white border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50"
                  >
                    All Features
                  </Link>
                  {feature.metadata?.doc && (
                    <Link
                      href={feature.metadata.doc}
                      className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-gray-700 transition-all duration-300 bg-white border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Learn More
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Section - Improved background */}
      <section className="relative bg-gray-50">
        <div className="container px-3 pt-4 pb-6 mx-auto sm:px-4 sm:pt-6 sm:pb-10">
          <div className="mx-auto max-w-6xl">

            {/* Feature Media Section - Enhanced with video/image switching */}
            {(feature.image || feature.youtubeId) && (
              <div className="mb-10  animate-fadeIn">
                <div className="p-3 bg-white border border-gray-200 rounded-lg sm:p-4">
                  <div className="relative w-full overflow-hidden rounded-lg">
                    {feature.youtubeId ? (
                      <div className="relative">
                        {showVideo ? (
                          // YouTube Video Player
                          <div className="relative pt-[56.25%] bg-gray-100">
                            <iframe
                              className="absolute inset-0 w-full h-full border-0 rounded-lg"
                              src={`https://www.youtube.com/embed/${feature.youtubeId}?autoplay=1&rel=0`}
                              title={feature.title}
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                            />
                          </div>
                        ) : (
                          // YouTube Thumbnail with Play Button
                          <div
                            className="relative pt-[56.25%] bg-gray-100 cursor-pointer group"
                            onClick={() => setShowVideo(true)}
                          >
                            <ImageFallback
                              src={getYouTubeThumbnail(feature.youtubeId)}
                              alt={`${feature.title} - Video Thumbnail`}
                              layout="fill"
                              className="object-cover rounded-lg transition-all duration-300 group-hover:scale-105"
                            />
                            {/* Play Button Overlay */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-red-600 rounded-full flex items-center justify-center transition-all duration-300 group-hover:bg-red-700 group-hover:scale-110">
                                <svg className="w-6 h-6 sm:w-8 sm:h-8 text-white ml-1" viewBox="0 0 24 24" fill="currentColor">
                                  <path d="M8 5v14l11-7z"/>
                                </svg>
                              </div>
                            </div>
                            {/* Video Badge */}
                            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                              <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                              </svg>
                              Video Demo
                            </div>
                          </div>
                        )}

                        {/* Video Controls */}
                        {showVideo && (
                          <div className="mt-3 flex justify-center">
                            <button
                              onClick={() => setShowVideo(false)}
                              className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-300 bg-white border border-gray-300 rounded-lg hover:border-gray-400 hover:bg-gray-50"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              Show Thumbnail
                            </button>
                          </div>
                        )}
                      </div>
                    ) : feature.image ? (
                      // Static Image Display
                      <div className="relative h-[300px] md:h-[400px] bg-gray-100 flex items-center justify-center rounded-lg">
                        <ImageFallback
                          src={feature.image}
                          alt={`${feature.title} - Feature Screenshot`}
                          layout="fill"
                          className="object-contain p-4 rounded-lg transition-all duration-300 hover:scale-105"
                        />
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            )}

            {/* Benefits Section */}
            {feature.benefits && feature.benefits.length > 0 && (
              <div className="mb-10 animate-fadeIn">
                <div className="p-3 bg-white border border-gray-200 rounded-lg sm:p-4">
                  <div className="mb-4 sm:mb-6">
                    <div className="flex items-center mb-4">
                      <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-semibold text-sm text-gray-900/80">
                        KEY BENEFITS
                      </span>
                    </div>
                  </div>
                  <div className="space-y-3 px-4">
                    {feature.benefits.map((benefit, i) => (
                      <div key={i} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-50 flex items-center justify-center mt-0.5">
                          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                          </svg>
                        </div>
                        <span className="flex-1 text-sm text-gray-700 leading-relaxed">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Call to Action Section */}
            <div className="mb-10 animate-fadeIn">
              <div className="p-6 bg-white border border-gray-200 rounded-lg sm:p-8 text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Start building amazing apps with {feature.title} today. Join thousands of developers who trust FluxBuilder.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Link
                    href="/download"
                    className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white transition-all duration-300 bg-blue-600 rounded-lg hover:bg-blue-700"
                  >
                    Download FluxBuilder
                  </Link>
                  <Link
                    href="/features"
                    className="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-gray-700 transition-all duration-300 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100"
                  >
                    Explore More Features
                  </Link>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>
    </Base>
  );
};

// Helper function to load features data
const loadFeaturesData = () => {
  try {
    const featuresPath = path.join(process.cwd(), 'public/config/features.json');
    if (!fs.existsSync(featuresPath)) {
      console.warn('Features data file not found:', featuresPath);
      return null;
    }
    const data = JSON.parse(fs.readFileSync(featuresPath, 'utf8'));
    return data;
  } catch (error) {
    console.error('Error loading features data:', error);
    return null;
  }
};

// Helper function to load language-specific features content
const loadFeaturesContent = (locale = 'en') => {
  try {
    const featuresPath = path.join(process.cwd(), `public/config/features.${locale}.json`);
    if (!fs.existsSync(featuresPath)) {
      console.warn(`Features content file not found for locale ${locale}:`, featuresPath);
      // Fallback to English
      if (locale !== 'en') {
        return loadFeaturesContent('en');
      }
      return null;
    }
    const data = JSON.parse(fs.readFileSync(featuresPath, 'utf8'));
    return data;
  } catch (error) {
    console.error(`Error loading features content for locale ${locale}:`, error);
    // Fallback to English
    if (locale !== 'en') {
      return loadFeaturesContent('en');
    }
    return null;
  }
};

// Helper function to find a feature by ID and merge with content
const findFeatureById = (featuresData, featuresContent, featureId) => {
  if (!featuresData || !featuresData.categories) {
    return null;
  }

  // Find the feature in the base data
  let baseFeature = null;
  let categoryId = null;

  for (const [catId, category] of Object.entries(featuresData.categories)) {
    if (category.features && Array.isArray(category.features)) {
      const feature = category.features.find(f => f.id === featureId);
      if (feature) {
        baseFeature = feature;
        categoryId = catId;
        break;
      }
    }
  }

  if (!baseFeature) {
    return null;
  }

  // Get the content for this feature
  const featureContent = featuresContent?.features?.[featureId] || {};

  // Merge base feature data with content
  const metadata = baseFeature.metadata || {};

  return {
    id: baseFeature.id,
    categoryId,
    metadata,
    title: featureContent.title || `Feature ${featureId}`,
    description: featureContent.content || featureContent.description || '',
    details: featureContent.details || featureContent.content || featureContent.description || '',
    benefits: featureContent.benefits || [],
    howItWorks: featureContent.howItWorks || [],
    useCases: featureContent.useCases || [],
    faqs: featureContent.faqs || [],
    relatedFeatures: featureContent.relatedFeatures || [],

    // Images and media
    image: featureContent.image || metadata.image || `/images/features/${featureId}.png`,
    icon: featureContent.icon || metadata.icon,

    // YouTube integration (make it accessible as both metadata.youtubeId and youtubeId)
    youtubeId: metadata.youtubeId,

    // Flags and status
    isNew: metadata.isNew || featureContent.isNew || false,
    isHot: metadata.isHot || featureContent.isHot || false,

    // Use cases (support both formats)
    useCaseIds: metadata.useCaseIds || featureContent.useCaseIds || [],

    // Include any additional properties from both sources
    ...baseFeature,
    ...featureContent
  };
};

export const getStaticProps = async ({ params }) => {
  const { slug } = params;
  const featuresData = loadFeaturesData();
  const featuresContent = loadFeaturesContent('en'); // Default to English for now

  // If no features data is found, return notFound
  if (!featuresData) {
    return {
      props: {
        feature: null,
        notFound: true
      }
    };
  }

  // Find the feature by slug/ID
  const feature = findFeatureById(featuresData, featuresContent, slug);

  // If no feature is found, return notFound
  if (!feature) {
    return {
      props: {
        feature: null,
        notFound: true
      }
    };
  }

  // Clean up the feature data to remove undefined values
  const cleanFeature = {
    id: feature.id,
    categoryId: feature.categoryId,
    title: feature.title,
    description: feature.description,
    details: feature.details,
    benefits: feature.benefits,
    howItWorks: feature.howItWorks,
    useCases: feature.useCases,
    faqs: feature.faqs,
    relatedFeatures: feature.relatedFeatures,
    useCaseIds: feature.useCaseIds,
    isNew: feature.isNew,
    isHot: feature.isHot,
    metadata: feature.metadata
  };

  // Only include optional properties if they have values
  if (feature.image) cleanFeature.image = feature.image;
  if (feature.icon) cleanFeature.icon = feature.icon;
  if (feature.youtubeId) cleanFeature.youtubeId = feature.youtubeId;

  // Return the feature data
  return {
    props: {
      feature: cleanFeature,
      notFound: false
    },
  };
};

export const getStaticPaths = async () => {
  const featuresData = loadFeaturesData();

  if (!featuresData || !featuresData.categories) {
    return {
      paths: [],
      fallback: false,
    };
  }

  // Extract all feature IDs from the JSON data
  const featureIds = [];
  Object.values(featuresData.categories).forEach(category => {
    if (category.features && Array.isArray(category.features)) {
      category.features.forEach(feature => {
        if (feature.id) {
          featureIds.push(feature.id);
        }
      });
    }
  });

  // Generate paths for all features
  const paths = featureIds.map((featureId) => ({
    params: {
      slug: featureId,
    },
  }));

  return {
    paths,
    fallback: false, // Set to false for static export compatibility
  };
};

export default FeatureDetailPage;
