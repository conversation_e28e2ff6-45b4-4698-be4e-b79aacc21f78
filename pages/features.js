import Base from "../layouts/Baseof";
import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import defaultFeatureCategories from '../public/config/features.json';
import FeatureCard from '../layouts/components/FeatureCard';
import { useOptimizedLanguageContext } from "../context/optimizedLanguageContext";
import { loadFeaturesForLanguage } from "../lib/utils/featuresLoader";
import { useTranslation } from "../lib/utils/i18n";
import dynamic from "next/dynamic";
import FeaturesList from '../layouts/components/features/list/FeaturesList';

// Simple loading component
const LoadingSpinner = () => (
  <div className="min-h-[200px] flex items-center justify-center">
    <div className="w-8 h-8 border-4 rounded-full border-primary border-t-transparent animate-spin" aria-label="Loading..."></div>
  </div>
);

// Feature categories for filtering - defined inside component to use translations
const getCategories = (t) => [
  { id: 'all', name: t('features.categories.all', 'All Features') },
  { id: 'core', name: t('features.categories.core', 'Core Features') },
  { id: 'integration', name: t('features.categories.integration', 'Integration') },
  { id: 'payment', name: t('features.categories.payment', 'Payment') },
  { id: 'ecommerce', name: t('features.categories.ecommerce', 'E-commerce') },
  { id: 'marketplace', name: t('features.categories.marketplace', 'Marketplace') },
  { id: 'management', name: t('features.categories.management', 'Management & Analytics') },
  { id: 'agency', name: t('features.categories.agency', 'Agency') }
];

// Standardized use cases for reuse across features
const standardUseCases = {
  // E-commerce
  'ecommerce': { id: 'ecommerce', name: 'E-commerce', color: 'blue' },
  'retail': { id: 'retail', name: 'Retail', color: 'blue' },
  'marketplace': { id: 'marketplace', name: 'Marketplace', color: 'blue' },

  // Content & Media
  'content': { id: 'content', name: 'Content', color: 'purple' },
  'media': { id: 'media', name: 'Media', color: 'purple' },
  'blogs': { id: 'blogs', name: 'Blogs', color: 'purple' },

  // Business
  'business': { id: 'business', name: 'Business', color: 'green' },
  'enterprise': { id: 'enterprise', name: 'Enterprise', color: 'green' },
  'saas': { id: 'saas', name: 'SaaS', color: 'green' },

  // Services
  'services': { id: 'services', name: 'Services', color: 'orange' },
  'booking': { id: 'booking', name: 'Booking', color: 'orange' },
  'delivery': { id: 'delivery', name: 'Delivery', color: 'orange' },

  // Social
  'social': { id: 'social', name: 'Social', color: 'pink' },
  'community': { id: 'community', name: 'Community', color: 'pink' },
  'networking': { id: 'networking', name: 'Networking', color: 'pink' },

  // Education
  'education': { id: 'education', name: 'Education', color: 'indigo' },
  'learning': { id: 'learning', name: 'Learning', color: 'indigo' },
  'training': { id: 'training', name: 'Training', color: 'indigo' },

  // Finance
  'finance': { id: 'finance', name: 'Finance', color: 'emerald' },
  'banking': { id: 'banking', name: 'Banking', color: 'emerald' },
  'payments': { id: 'payments', name: 'Payments', color: 'emerald' },

  // Health
  'health': { id: 'health', name: 'Health', color: 'red' },
  'fitness': { id: 'fitness', name: 'Fitness', color: 'red' },
  'medical': { id: 'medical', name: 'Medical', color: 'red' },

  // Travel
  'travel': { id: 'travel', name: 'Travel', color: 'amber' },
  'hospitality': { id: 'hospitality', name: 'Hospitality', color: 'amber' },
  'events': { id: 'events', name: 'Events', color: 'amber' },

  // Utility
  'utility': { id: 'utility', name: 'Utility', color: 'gray' },
  'productivity': { id: 'productivity', name: 'Productivity', color: 'gray' },
  'tools': { id: 'tools', name: 'Tools', color: 'gray' },
};

const FeaturesPage = () => {
  const router = useRouter();
  const featureGridRef = useRef(null);
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedFeature, setExpandedFeature] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { currentLanguage } = useOptimizedLanguageContext();
  const [featureCategories, setFeatureCategories] = useState(defaultFeatureCategories);
  const { t } = useTranslation();

  // Dynamic imports with proper loading states and error boundaries
  const FinalCTASection = dynamic(() => import('../layouts/fluxbuilder/FinalCTASection'), {
    loading: () => <LoadingSpinner />,
    ssr: true
  });

  const TrustAndSocialProofSection = dynamic(() => import('../layouts/fluxbuilder/TrustAndSocialProofSection'), {
    loading: () => <LoadingSpinner />,
    ssr: true
  });

  const FAQSection = dynamic(() => import('../layouts/fluxbuilder/FAQSection'), {
    loading: () => <LoadingSpinner />,
    ssr: true
  });

  // Get translated categories
  const categories = getCategories(t);

  // Handle URL search parameters - simplified to prevent conflicts
  useEffect(() => {
    if (!router.isReady) return;

    const { search, category } = router.query;

    if (search && search !== searchTerm) {
      setSearchTerm(search);
    }

    if (category && category !== activeCategory) {
      const validCategory = categories.find(cat => cat.id === category);
      if (validCategory) {
        setActiveCategory(category);
      }
    }
  }, [router.isReady, router.query, categories]);

  // Load language-specific features
  useEffect(() => {
    const loadFeatures = async () => {
      try {
        console.log(`Features page: Loading features for ${currentLanguage}`);
        const features = await loadFeaturesForLanguage(currentLanguage);

        if (features && features.categories) {
          console.log(`Features page: Successfully loaded features for ${currentLanguage}`);
          setFeatureCategories(features.categories);
        } else {
          console.warn(`Features page: No categories found in features for ${currentLanguage}, falling back to default`);
          setFeatureCategories(defaultFeatureCategories.categories || {});
        }
      } catch (error) {
        console.error(`Features page: Failed to load features for ${currentLanguage}:`, error);
        if (process.env.NODE_ENV === 'development') {
          console.error(`Error loading features for ${currentLanguage}: ${error.message}`);
        }
        setFeatureCategories(defaultFeatureCategories.categories || {});
      }
    };

    loadFeatures();
  }, [currentLanguage]);

  // Adjust scroll position for sticky header when clicking category buttons
  const handleCategorySelect = (categoryId) => {
    setActiveCategory(categoryId);
    setSearchTerm('');

    // Simple scroll without preventing default behavior
    setTimeout(() => {
      const headerHeight = 120;

      if (categoryId === 'all') {
        if (featureGridRef.current) {
          const targetPosition = featureGridRef.current.offsetTop - headerHeight;
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      } else {
        const categoryElement = document.getElementById(`category-${categoryId}`);
        if (categoryElement) {
          const targetPosition = categoryElement.offsetTop - headerHeight;
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  };

  // Filter features based on active category and search term
  const filteredFeatures = useMemo(() => {
    return Object.keys(featureCategories).reduce((acc, categoryKey) => {
      if (!featureCategories[categoryKey] ||
          (activeCategory !== 'all' && activeCategory !== categoryKey)) {
        return acc;
      }

      if (!featureCategories[categoryKey].features) {
        console.warn(`Category ${categoryKey} does not have a features array`);
        return acc;
      }

      const filteredCategoryFeatures = featureCategories[categoryKey].features.filter(feature => {
        const title = feature.title || '';
        const description = feature.description || '';

        return title.toLowerCase().includes(searchTerm.toLowerCase()) ||
               description.toLowerCase().includes(searchTerm.toLowerCase());
      });

      if (filteredCategoryFeatures.length > 0) {
        acc[categoryKey] = {
          ...featureCategories[categoryKey],
          features: filteredCategoryFeatures
        };
      }

      return acc;
    }, {});
  }, [featureCategories, activeCategory, searchTerm]);

  // Simplified feature expansion handler
  const handleFeatureExpansion = (featureId) => {
    const element = document.getElementById(featureId);
    if (element) {
      setExpandedFeature(prev => prev === featureId ? null : featureId);
      
      setTimeout(() => {
        const rect = element.getBoundingClientRect();
        const headerHeight = 120;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const elementTop = rect.top + scrollTop;
        const finalScrollPosition = elementTop - headerHeight - 20;

        window.scrollTo({
          top: finalScrollPosition,
          behavior: 'smooth'
        });
      }, 300);
    }
  };

  // Simplified hash handling without router conflicts
  useEffect(() => {
    if (!router.isReady) return;

    const hash = window.location.hash;
    if (hash) {
      const featureId = hash.replace(/^#/, '');
      
      // Reset filters when navigating to a specific feature
      setSearchTerm('');
      setActiveCategory('all');
      
      setTimeout(() => {
        const element = document.getElementById(featureId);
        if (element) {
          setExpandedFeature(featureId);
          
          const rect = element.getBoundingClientRect();
          const headerHeight = 120;
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const elementTop = rect.top + scrollTop;
          const finalScrollPosition = elementTop - headerHeight - 20;

          window.scrollTo({
            top: finalScrollPosition,
            behavior: 'smooth'
          });
        }
      }, 500);
    }
  }, [router.isReady]);

  // Simple scroll handler for nav shadow
  useEffect(() => {
    const handleScroll = () => {
      const navElement = document.getElementById('category-nav');
      if (navElement) {
        if (window.scrollY > 300) {
          navElement.classList.add('shadow-md');
        } else {
          navElement.classList.remove('shadow-md');
        }
      }
    };

    handleScroll();
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <Base
      title={`${t('features.pageTitle', 'Features')} | FluxBuilder - ${t('common.appBuilderWithoutCode', 'App Builder Without Code')}`}
      meta_title={`${t('features.pageTitle', 'Features')} | FluxBuilder - ${t('common.professionalMobileAppBuilder', 'Professional Mobile App Builder')}`}
      description={t('features.pageDescription', 'Explore all the powerful features of FluxBuilder that let you create mobile apps without writing a single line of code.')}
      language={currentLanguage}
    >
      {/* Feature Grid Section - Enhanced Background & Padding - Mobile Optimized */}
      <section ref={featureGridRef} className="relative bg-neutral-100">
        {/* Subtle Gradient & More Padding - Mobile Optimized */}
        <div className="mx-0 max-w[100%] pt-6 sm:pt-8 md:pt-12 pb-1 container-home home-hero hero4 text-center">
          <div className="mb-2 sm:mb-4">
            <div className="max-w-5xl mx-auto mt-6 sm:mt-8 md:mt-12 animate-fadeIn">
              <div className="mb-6 text-center sm:mb-8 md:mb-12">
                <h1 className="mt-20 mb-3 text-3xl font-light text-transparent sm:mb-4 md:mb-6 sm:text-4xl md:text-5xl lg:text-6xl bg-gradient-to-r from-primary/90 to-primary bg-clip-text">
                  
                </h1>
                <p className="max-w-[700px] m-auto text-sm sm:text-base md:text-lg text-neutral-600 dark:text-neutral-400 px-4 sm:px-0">
                  {t('features.subheading', 'FluxBuilder provides comprehensive features to help you create professional, engaging mobile apps without any technical expertise.')}
                </p>
              </div>

              {/* Search Input - Modernized with Clear Button - Mobile Optimized */}
              <div className="relative w-full max-w-lg px-4 mx-auto mb-6 sm:mb-10 md:mb-12 sm:px-0 animate-fadeIn">
                <div className="absolute inset-y-0 flex items-center pl-2 pointer-events-none left-2 ">
                  <svg className="w-4 h-4 text-white/50 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-full p-3 pl-10 pr-10 text-sm text-white transition bg-white/20 rounded-lg sm:p-3 sm:pl-12 sm:pr-12 sm:text-base sm:rounded-xl placeholder-white/70 focus:outline-none focus:ring-0 border-0"
                  placeholder={t('features.searchPlaceholder', 'Search features...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {/* Clear button - only shows when there's text - Mobile Optimized */}
                {searchTerm && (
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-white transition-colors sm:pr-4 hover:text-white/80"
                    onClick={() => {
                      setSearchTerm('');
                      setActiveCategory('all');
                    }}
                    aria-label={t('features.clearSearch', 'Clear search')}
                  >
                    <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sticky Horizontal Category Navigation */}
        <div id="category-nav" className="sticky top-[0px] py-1 z-40 bg-white mb-6 transition-all duration-300 ease-in-out">
          <div className="container px-4 mx-auto ">
            <div className="py-[2px] overflow-x-auto scrollbar-hide ">
              <div className="flex flex-wrap justify-center gap-2 md:gap-3">
                {categories.map((category) => {
                  const count = category.id === 'all'
                    ? Object.values(featureCategories).reduce((total, cat) => total + (cat.features?.length || 0), 0)
                    : featureCategories[category.id]?.features?.length || 0;

                  return (
                    <button
                      key={category.id}
                      className={`whitespace-nowrap transition-all duration-250 ease-in-out relative flex items-center px-3 sm:px-5 py-2 sm:py-3.5 text-sm sm:text-base font-medium min-w-[68px] rounded-lg hover:-translate-y-0.5 ${
                        activeCategory === category.id
                          ? 'text-blue-700 active bg-gray-100'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                      onClick={() => handleCategorySelect(category.id)}
                      aria-label={t('features.selectCategory', 'Select category: ') + category.name}
                    >
                      <span className="font-medium">{category.name}</span>
                      <span className={`absolute top-[4px] right-[2px] text-[0.65rem] leading-none inline-flex items-center justify-center rounded-full min-w-[1rem] h-[1rem] px-[0.25rem] transition-all duration-200 ease-in-out ${
                        activeCategory === category.id
                          ? 'text-gray-800'
                          : 'text-gray-400'
                      }`}>
                        {count}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        <div className="container px-3 pt-4 pb-6 mx-auto sm:px-4 sm:pt-6 sm:pb-10">
          <div className="mx-auto max-w-6xl">
            {/* Feature content layout */}
            <div className="flex flex-col gap-6 sm:gap-8">
              {/* Feature List Content */}
              <div className="flex-1 min-w-0">
                {Object.keys(filteredFeatures).length > 0 ? (
                  Object.keys(filteredFeatures).map((category) => (
                    <div key={category} id={`category-${category}`} className="mb-10 sm:mb-16 md:mb-20 scroll-mt-20 sm:scroll-mt-28 animate-fadeIn">
                      <div className="mb-4 sm:mb-6">
                        {/* Category Title */}
                        <div className="flex items-center mb-4">
                          {/* Category Icon */}
                          {category === 'all' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                          )}
                          {category === 'core' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                            </svg>
                          )}
                          {category === 'integration' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          )}
                          {category === 'payment' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                          )}
                          {category === 'ecommerce' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                          )}
                          {category === 'marketplace' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                          )}
                          {category === 'management' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                          )}
                          {category === 'agency' && (
                            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                            </svg>
                          )}
                          <span className="font-semibold text-sm text-gray-900/80">
                            {filteredFeatures[category].title.toUpperCase()}
                          </span>
                        </div>
                      </div>

                      <div className="p-3 bg-white border border-gray-200 rounded-lg sm:p-4">
                        {filteredFeatures[category].features.map((feature, index) => {
                          const featureId = feature.id;
                          return (
                            <FeatureCard
                              key={index}
                              feature={feature}
                              index={index}
                              featureId={featureId}
                              isExpanded={expandedFeature === featureId}
                              setExpandedFeature={setExpandedFeature}
                              standardUseCases={standardUseCases}
                            />
                          );
                        })}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="py-10 text-center bg-white border border-gray-200 rounded-lg sm:py-16 animate-fadeIn">
                    <svg className="w-12 h-12 mx-auto mb-3 text-gray-300 sm:w-16 sm:h-16 sm:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="mb-1 text-lg font-bold text-gray-700 sm:mb-2 sm:text-xl">{t('features.noFeaturesFound', 'No features found')}</h3>
                    <p className="text-sm text-gray-500 sm:text-base">{t('features.tryAdjusting', 'Try adjusting your search or filter criteria')}</p>
                    <button
                      className="px-3 sm:px-4 py-1.5 sm:py-2 mt-3 sm:mt-4 text-xs sm:text-sm font-medium text-blue-600 rounded-lg bg-blue-50 hover:bg-blue-100"
                      onClick={() => {
                        setSearchTerm('');
                        setActiveCategory('all');
                        if (typeof window !== 'undefined' && window.location.hash) {
                          window.history.replaceState(
                            null,
                            document.title,
                            window.location.pathname + window.location.search
                          );
                        }
                      }}
                      aria-label={t('features.resetFilters', 'Reset filters')}
                    >
                      {t('features.resetFilters', 'Reset filters')}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust and Social Proof Section */}
      <TrustAndSocialProofSection />

      {/* FAQ Section */}
      <FAQSection />

      {/* Final CTA Section */}
      <FinalCTASection t={t} />
    </Base>
  );
};

export default FeaturesPage;
