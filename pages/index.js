import Base from "../layouts/Baseof";
import Script from "next/script";
import { useMemo, useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { getSinglePage } from "@/lib/contentParser";
import featuredShowcase from '@/config/featured-showcase.json';
import { useTranslation } from "@/lib/utils/i18n";
import { markStart, markEnd, logMetrics } from "@/lib/utils/performance";

// Import critical components directly
import HeroSection from '../layouts/fluxbuilder/HeroSection';
import MiniFeaturedApps from '../layouts/fluxbuilder/MiniFeaturedApps';

// Lightweight loading component with skeleton UI
const LoadingPlaceholder = ({ type = "default", className = "" }) => {
  switch (type) {
    case "features":
      return (
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-[400px] bg-gray-50 rounded-xl animate-pulse">
              <div className="h-48 bg-gray-100 rounded-t-xl" />
              <div className="p-6 space-y-4">
                <div className="h-4 bg-gray-100 rounded w-3/4" />
                <div className="h-4 bg-gray-100 rounded w-1/2" />
                <div className="h-20 bg-gray-100 rounded" />
              </div>
            </div>
          ))}
        </div>
      );
    case "platforms":
      return (
        <div className={`flex flex-wrap justify-center gap-8 ${className}`}>
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="w-32 h-12 bg-gray-50 rounded animate-pulse" />
          ))}
        </div>
      );
    default:
      return <div className={`w-full h-64 bg-gray-50 rounded-xl animate-pulse ${className}`} />;
  }
};

// Optimized dynamic imports with better loading states
const FeaturesSection = dynamic(
  () => import("../layouts/fluxbuilder/FeaturesSection"),
  {
    loading: () => <LoadingPlaceholder type="features" className="mt-24 mb-16" />,
    ssr: false
  }
);

const PlatformIntegrationSection = dynamic(
  () => import("../layouts/fluxbuilder/PlatformIntegrationSection"),
  {
    loading: () => <LoadingPlaceholder type="platforms" className="py-16" />,
    ssr: false
  }
);

const TrustAndSocialProofSection = dynamic(
  () => import("../layouts/fluxbuilder/TrustAndSocialProofSection"),
  {
    loading: () => <LoadingPlaceholder className="py-16" />,
    ssr: false
  }
);

const WhyFluxBuilderSection = dynamic(
  () => import("../layouts/fluxbuilder/WhyFluxBuilderSection"),
  {
    loading: () => <LoadingPlaceholder className="py-16" />,
    ssr: false
  }
);

const FAQSection = dynamic(
  () => import("../layouts/fluxbuilder/FAQSection"),
  {
    loading: () => <LoadingPlaceholder className="py-16" />,
    ssr: false
  }
);

const FinalCTASection = dynamic(
  () => import("../layouts/fluxbuilder/FinalCTASection"),
  {
    loading: () => <LoadingPlaceholder className="py-16" />,
    ssr: false
  }
);

const HomePage = ({ examples }) => {
  markStart('HomePage:render');
  const [websiteUrl, setWebsiteUrl] = useState("");
  const { t } = useTranslation();
  const [mounted, setMounted] = useState(false);

  // Track component mounting for hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Optimize featured examples processing
  const featuredExamples = useMemo(() => {
    markStart('HomePage:processFeaturedExamples');

    // Filter examples that are in our featured apps list
    const filtered = examples?.filter(example =>
      featuredShowcase.apps.includes(example.frontmatter.bundleId)
    ) || [];

    // If we have less than 5 examples, add more from the examples list
    if (filtered.length < 5) {
      const remainingExamples = examples?.filter(example =>
        !filtered.some(f => f.frontmatter.bundleId === example.frontmatter.bundleId)
      ) || [];

      // Sort remaining examples by date and take enough to reach 5
      const additionalExamples = remainingExamples
        .sort((a, b) => new Date(b.frontmatter.date) - new Date(a.frontmatter.date))
        .slice(0, 5 - filtered.length);

      filtered.push(...additionalExamples);
    }

    // Sort by date in descending order (latest first)
    const result = filtered.sort((a, b) => {
      const dateA = new Date(a.frontmatter.date);
      const dateB = new Date(b.frontmatter.date);
      return dateB - dateA;
    });

    markEnd('HomePage:processFeaturedExamples');
    return result;
  }, [examples]);

  // Handle form submission
  const handleTryFluxBuilder = (e) => {
    e.preventDefault();
    if (websiteUrl) {
      window.open(`https://web.fluxbuilder.com/?url=${encodeURIComponent(websiteUrl)}`, '_blank');
    }
  };

  // Log performance metrics after initial render
  useEffect(() => {
    if (mounted) {
      const timer = setTimeout(() => {
        logMetrics();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [mounted]);

  return (
    <Base
      title="FluxBuilder - Create Native Mobile Apps from Your Website | No Coding"
      meta_title="FluxBuilder | #1 Website to App Converter | iOS & Android"
      description="Transform your website into a high-performance native mobile app in minutes. No coding required. Get push notifications, offline mode & more. Perfect for eCommerce, blogs, and businesses."
      image="/images/og-image.png"
      noindex={false}
      canonical="https://fluxbuilder.com"
    >
      {/* Schema markup */}
      <Script
        id="homepage-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "FluxBuilder",
            "description": "Professional mobile app builder that converts websites into native mobile applications with advanced features like push notifications, offline mode, and seamless integration.",
            "url": "https://fluxbuilder.com",
            "image": "https://fluxbuilder.com/images/og-image.png",
            "applicationCategory": "Mobile App Builder",
            "operatingSystem": "Android, iOS",
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "26000",
              "bestRating": "5",
              "worstRating": "1"
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD",
              "availability": "https://schema.org/InStock",
              "url": "https://fluxbuilder.com/download"
            }
          })
        }}
        strategy="afterInteractive"
      />

      {/* Above the fold content - render immediately */}
      <HeroSection
        websiteUrl={websiteUrl}
        setWebsiteUrl={setWebsiteUrl}
        handleTryFluxBuilder={handleTryFluxBuilder}
      />

      {/* Featured Apps - render immediately for SEO */}
      <MiniFeaturedApps featuredExamples={featuredExamples} />

      {/* Lazy loaded sections */}
      <PlatformIntegrationSection />
      <TrustAndSocialProofSection />
      <WhyFluxBuilderSection />
      <FeaturesSection />
      <FAQSection />
      <FinalCTASection />
    </Base>
  );
};

export default HomePage;

// Optimize static props generation
export const getStaticProps = async () => {
  markStart('getStaticProps');

  // Limit the number of examples to improve performance
  const MAX_EXAMPLES = 50;
  const allExamples = getSinglePage("content/examples");
  const examples = allExamples?.slice(0, MAX_EXAMPLES) || [];

  markEnd('getStaticProps');

  return {
    props: {
      examples: examples || [],
    }
  };
};
