<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Why FluxBuilder Section - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="max-w-6xl mx-auto p-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">New Section Added: "Why use FluxBuilder?"</h1>
            <p class="text-xl text-gray-600">A comprehensive comparison section to educate users about FluxBuilder's advantages</p>
        </div>

        <!-- Demo of the new section -->
        <section class="relative py-16 sm:py-20 lg:py-24 bg-gradient-to-b from-white to-gray-50 rounded-2xl border border-gray-200">
            <!-- Background decorations -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none rounded-2xl">
                <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                <div class="absolute top-1/3 right-1/4 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                <div class="absolute bottom-1/4 left-1/3 w-64 h-64 bg-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
            </div>

            <div class="container relative px-4 mx-auto sm:px-6 lg:px-8">
                <!-- Section Header -->
                <div class="max-w-3xl mx-auto text-center mb-12 sm:mb-16">
                    <h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        Why use FluxBuilder?
                    </h2>
                    <p class="text-lg sm:text-xl text-gray-600 leading-relaxed">
                        See how FluxBuilder stands out from other app builders with professional features and unlimited possibilities.
                    </p>
                </div>

                <!-- Comparison Table -->
                <div class="max-w-5xl mx-auto">
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
                        <!-- Table Header -->
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8">
                            <div class="grid grid-cols-3 gap-4 items-center">
                                <div class="text-left">
                                    <h3 class="text-lg sm:text-xl font-semibold text-white">Features</h3>
                                </div>
                                <div class="text-center">
                                    <h3 class="text-lg sm:text-xl font-semibold text-white/90">Other App Builders</h3>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                                        <span class="text-lg sm:text-xl font-bold text-white">FluxBuilder</span>
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comparison Rows -->
                        <div class="divide-y divide-gray-100">
                            <!-- Feature 1 -->
                            <div class="grid grid-cols-3 gap-4 items-center px-6 py-6 hover:bg-gray-50/50 transition-colors duration-200">
                                <div class="flex items-start gap-3">
                                    <span class="text-2xl flex-shrink-0 mt-1">🔓</span>
                                    <span class="text-sm sm:text-base text-gray-800 font-medium leading-relaxed">
                                        Include Source code to unlock all customization
                                    </span>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100">
                                        <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Feature 2 -->
                            <div class="grid grid-cols-3 gap-4 items-center px-6 py-6 hover:bg-gray-50/50 transition-colors duration-200">
                                <div class="flex items-start gap-3">
                                    <span class="text-2xl flex-shrink-0 mt-1">🏢</span>
                                    <span class="text-sm sm:text-base text-gray-800 font-medium leading-relaxed">
                                        Support Agency white-label and Dashboard
                                    </span>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100">
                                        <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- Feature 3 -->
                            <div class="grid grid-cols-3 gap-4 items-center px-6 py-6 hover:bg-gray-50/50 transition-colors duration-200">
                                <div class="flex items-start gap-3">
                                    <span class="text-2xl flex-shrink-0 mt-1">☁️</span>
                                    <span class="text-sm sm:text-base text-gray-800 font-medium leading-relaxed">
                                        Unlimited Build On Cloud
                                    </span>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100">
                                        <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <!-- More features would continue here... -->
                        </div>

                        <!-- Bottom CTA -->
                        <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-8 text-center">
                            <p class="text-gray-600 mb-4 text-sm sm:text-base">
                                Ready to experience the FluxBuilder difference?
                            </p>
                            <div class="flex flex-col sm:flex-row gap-3 justify-center items-center">
                                <button class="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                    Download FluxBuilder
                                </button>
                                <button class="px-6 py-3 bg-white border border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                                    View All Features
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Benefits -->
                <div class="max-w-4xl mx-auto mt-12 sm:mt-16">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                            <p class="text-gray-600 text-sm">Build and deploy your app in minutes, not months</p>
                        </div>

                        <div class="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Professional Grade</h3>
                            <p class="text-gray-600 text-sm">Enterprise-level features and security standards</p>
                        </div>

                        <div class="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Dedicated Support</h3>
                            <p class="text-gray-600 text-sm">24/7 expert support and comprehensive documentation</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Implementation Summary -->
        <div class="mt-12 bg-white rounded-xl p-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 Implementation Summary</h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">✅ What Was Added:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• New WhyFluxBuilderSection.js component</li>
                        <li>• Comparison table with 6 key features</li>
                        <li>• Visual checkmarks vs X marks</li>
                        <li>• Animated background effects</li>
                        <li>• CTA buttons for conversion</li>
                        <li>• Additional benefits cards</li>
                        <li>• Full translation support</li>
                        <li>• Responsive design</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">🎯 Key Features Highlighted:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>🔓 Include Source code to unlock all customization</li>
                        <li>🏢 Support Agency white-label and Dashboard</li>
                        <li>☁️ Unlimited Build On Cloud</li>
                        <li>🛠️ Long term support and maintain</li>
                        <li>🚀 Build completely production app ready, not just the UI</li>
                        <li>⚡ True native performance with Flutter framework</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
