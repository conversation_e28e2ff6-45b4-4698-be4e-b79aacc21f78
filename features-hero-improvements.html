<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Features Hero Section Improvements - Before vs After</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .label {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .before-label {
            background: #ef4444;
            color: white;
        }
        
        .after-label {
            background: #10b981;
            color: white;
        }

        /* Simulate the old bright background */
        .old-hero {
            background: #f5f5f5; /* neutral-100 equivalent */
            padding: 60px 20px;
            border-radius: 12px;
        }

        /* New improved background */
        .new-hero {
            background: linear-gradient(to bottom, #f8fafc, #ffffff); /* slate-50 to white */
            padding: 60px 20px;
            border-radius: 12px;
        }

        /* Button styles */
        .btn-primary {
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            padding: 12px 24px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* Contrast indicators */
        .contrast-poor {
            position: relative;
        }

        .contrast-poor::after {
            content: '⚠️ Poor Contrast';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ef4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .contrast-good {
            position: relative;
        }

        .contrast-good::after {
            content: '✅ Good Contrast';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Features Hero Section Improvements</h1>
            <p class="text-xl text-gray-600">Fixing background contrast and removing unnecessary icons for better UX</p>
        </div>

        <!-- Problem Statement -->
        <div class="bg-white rounded-xl p-8 mb-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🚨 Issues Identified</h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="font-semibold text-yellow-800 mb-2">Hero Section Problems:</h3>
                <ul class="text-yellow-700 space-y-1">
                    <li>• <strong>Thiếu tương phản:</strong> Background quá nổi (bg-neutral-100) làm button khó nhìn</li>
                    <li>• <strong>Icon không cần thiết:</strong> Icon phía sau button không thể hiện rõ action</li>
                    <li>• <strong>Không đồng nhất:</strong> Icon style khác với các trang khác</li>
                </ul>
            </div>
        </div>

        <!-- Before Design -->
        <div class="comparison-section before">
            <div class="before-label label">❌ BEFORE - Poor Contrast & Unnecessary Icons</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Bright Background with Cluttered Buttons</h3>
            
            <div class="old-hero contrast-poor">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">Feature Title</h1>
                    <p class="text-gray-600 mb-8 max-w-2xl mx-auto">
                        This is a sample feature description that explains what this feature does and how it benefits users.
                    </p>
                    <div class="flex flex-wrap justify-center gap-4">
                        <button class="btn-primary">
                            Try for Free
                        </button>
                        <button class="btn-secondary">
                            All Features
                        </button>
                        <button class="btn-secondary">
                            Learn More
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-red-800 mb-2">Problems with the original design:</h4>
                <ul class="text-red-700 space-y-1">
                    <li>• <strong>Poor contrast:</strong> Bright neutral-100 background makes buttons blend in</li>
                    <li>• <strong>Unnecessary icons:</strong> External link icon doesn't add value to "Learn More"</li>
                    <li>• <strong>Inconsistent design:</strong> Icons not used consistently across the site</li>
                    <li>• <strong>Visual clutter:</strong> Too many visual elements competing for attention</li>
                    <li>• <strong>Accessibility issues:</strong> Low contrast affects readability</li>
                </ul>
            </div>
        </div>

        <!-- After Design -->
        <div class="comparison-section after">
            <div class="after-label label">✅ AFTER - Better Contrast & Clean Buttons</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Subtle Background with Clean Button Design</h3>
            
            <div class="new-hero contrast-good">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">Feature Title</h1>
                    <p class="text-gray-600 mb-8 max-w-2xl mx-auto">
                        This is a sample feature description that explains what this feature does and how it benefits users.
                    </p>
                    <div class="flex flex-wrap justify-center gap-4">
                        <button class="btn-primary">
                            Try for Free
                        </button>
                        <button class="btn-secondary">
                            All Features
                        </button>
                        <button class="btn-secondary">
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-green-800 mb-2">Improvements made:</h4>
                <ul class="text-green-700 space-y-1">
                    <li>• <strong>Better contrast:</strong> Gradient from slate-50 to white provides subtle background</li>
                    <li>• <strong>Clean buttons:</strong> Removed unnecessary icons for cleaner appearance</li>
                    <li>• <strong>Consistent design:</strong> Matches button patterns used across the site</li>
                    <li>• <strong>Reduced clutter:</strong> Focus on content and clear call-to-actions</li>
                    <li>• <strong>Better accessibility:</strong> Improved contrast ratios for all users</li>
                </ul>
            </div>
        </div>

        <!-- Technical Implementation -->
        <div class="bg-white rounded-xl p-8 border border-gray-200 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 Technical Changes Made</h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Background Changes:</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                            <h4 class="font-semibold text-red-800 mb-2">Before:</h4>
                            <code class="text-sm text-red-700">className="relative bg-neutral-100"</code>
                            <p class="text-sm text-red-600 mt-2">Too bright, poor contrast with buttons</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                            <h4 class="font-semibold text-green-800 mb-2">After:</h4>
                            <code class="text-sm text-green-700">className="relative bg-gradient-to-b from-slate-50 to-white"</code>
                            <p class="text-sm text-green-600 mt-2">Subtle gradient, better contrast</p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Button Changes:</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                            <h4 class="font-semibold text-red-800 mb-2">Before:</h4>
                            <code class="text-sm text-red-700">Learn More + &lt;svg&gt;...&lt;/svg&gt;</code>
                            <p class="text-sm text-red-600 mt-2">Unnecessary external link icon</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                            <h4 class="font-semibold text-green-800 mb-2">After:</h4>
                            <code class="text-sm text-green-700">Learn More</code>
                            <p class="text-sm text-green-600 mt-2">Clean text-only button</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Design Principles -->
        <div class="bg-white rounded-xl p-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">🎨 Design Principles Applied</h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Better Contrast</h3>
                    <p class="text-gray-600 text-sm">Subtle gradient background provides better contrast for buttons and text readability</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Clean Interface</h3>
                    <p class="text-gray-600 text-sm">Removed unnecessary icons to create cleaner, more focused user interface</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Consistent Design</h3>
                    <p class="text-gray-600 text-sm">Aligned button styling with the rest of the site for cohesive user experience</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
