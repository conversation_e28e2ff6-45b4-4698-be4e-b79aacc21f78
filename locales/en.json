{"common": {"getStarted": "Get Started", "learnMore": "Learn More", "downloadNow": "Download Now", "tryForFree": "Try For Free", "getFreeDownload": "Get Free Download", "changeLanguage": "Change the language", "improveReadability": "Improve text readability", "on": "On", "off": "Off", "allRightsReserved": "All rights reserved", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "appBuilderWithoutCode": "App Builder Without Code", "professionalMobileAppBuilder": "Professional Mobile App Builder", "search": "Search", "easySetup": "Easy Setup", "appStoreApproval": "App Store Approval"}, "header": {"features": "Features", "pricing": "Pricing", "showcase": "Showcase", "blog": "Blog", "resources": "Resources", "agency": "Agency", "download": "Download"}, "pricing": {"hero": {"title": "Transform Your Business with a Mobile App", "subtitle": "Choose a plan that fits your needs and start building your iOS & Android app today, no coding required.", "monthly": "Monthly", "yearly": "Yearly", "savePercent": "Save 20%"}, "trustBadges": {"moneyBack": "15-Day Money Back", "moneyBackDesc": "No questions asked", "customerRating": "4.9/5 Rating", "customerRatingDesc": "From 1,000+ reviews", "activeUsers": "50,000+ Users", "activeUsersDesc": "Across 120+ countries", "support": "24/7 Support", "supportDesc": "Always here to help", "trustedBy": "Trusted by companies worldwide"}, "plans": {"starter": {"name": "Starter", "description": "Perfect for individuals getting started with app development.", "buttonText": "Free Download"}, "basic": {"name": "Basic", "description": "For growing businesses that need more features and capacity.", "normalPrice": "normally $39", "normalYearlyPrice": "normally $468/year", "integration": "Per 1 website integration", "promoCode": "Promo Code", "buttonText": "Buy Now", "yearlyBilling": "Billed yearly", "save": "Save $188"}, "professional": {"name": "Professional", "description": "For large organizations with advanced requirements.", "normalPrice": "normally $49", "normalYearlyPrice": "normally $588/year", "integration": "Per 1 website integration", "promoCode": "Promo Code", "buttonText": "Buy Now", "yearlyBilling": "Billed yearly", "save": "Save $236"}, "whatIncluded": "What's included:"}, "agency": {"badge": "For Agencies", "title": "Agency Solutions", "description": "Start your own app development business with our white-label solution. Create unlimited apps for your clients without writing a single line of code.", "benefits": {"unlimited": "Unlimited app creation", "dashboard": "Client management dashboard", "whiteLabel": "White-label app services"}, "buttonText": "Explore Agency Plans"}, "lifetime": {"badge": "Limited Time Offer", "title": "Lifetime Access", "description": "Save up to 80% with a one-time payment. Get permanent access to all Pro features and unlimited cloud builds without recurring charges.", "limitedOffer": "Limited Time Offer", "offerEnds": "Offer ends in:", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "limitedSpots": "* Only a limited number of licenses available at this price", "reopenOffer": "Reopen lifetime offer", "specialOffer": "Special Offer", "benefits": {"oneTime": "One-time payment, lifetime access", "proFeatures": "All Pro features included", "updates": "Unlimited templates included", "unlimitedBuilds": "Unlimited cloud builds"}, "buttonText": "Get Lifetime Deal", "pageTitle": "FluxBuilder Lifetime Plans - Single, MAX & Unlimited Options | One-Time Payment", "pageDescription": "Get lifetime access to FluxBuilder with flexible options: Single app ($199), MAX Lifetime (from $139/app), or Unlimited access ($900). Build iOS & Android apps with no recurring fees.", "endingSoon": "LIMITED OFFER - ENDING SOON 🔥", "lifetimeAccess": "Lifetime Access to", "singleApp": "Single Lifetime", "maxLifetime": "MAX Lifetime", "unlimitedLifetime": "Ultra Lifetime", "oneTime": "one time", "optionsAvailable": "Single App, MAX & Unlimited Options Available", "getLifetimeDeal": "Get the Lifetime deal today!", "riskFreeGuarantee": "30-Day Risk-Free Guarantee", "easySetup": "100% easy setup, no technical skills needed", "noCoding": "0% coding knowledge required", "appStoreApproval": "App Store & Google Play approval guaranteed", "choosePlan": "Choose Your Lifetime Plan", "selectPerfect": "Select the perfect lifetime option for your needs - from single app to unlimited access", "pricingInUSD": "All pricing is in USD. You can change plans or cancel your account at any time.", "biggestOffer": "Don't Miss Out on Our BIGGEST Offer of the Year!", "specialOpportunity": "This is a special opportunity to save huge on FluxBuilder plans.", "limitedTimeOffer": "Limited time offer - These deals will expire soon!", "riskFree": "100% Risk-Free", "tryFluxBuilder": "Try FluxBuilder for 30 days. If you're not completely satisfied, we'll refund your purchase.", "easySetupFull": "Get your app up and running in minutes with our intuitive interface. 0% coding knowledge required.", "appStoreApprovalFull": "Apple AppStore and Google Play approval guaranteed or refund within 15 days.", "singleDescription": "Perfect for individual projects or small businesses", "maxDescription": "Ideal for agencies and developers with multiple projects", "unlimitedDescription": "Perfect for large agencies with unlimited projects", "features": {"singleWebsite": "1 Website Integration", "lifetimeIosAndroid": "Lifetime iOS & Android", "allPremiumFeatures": "All Premium Features", "multipleWebsites": "3-20 Website Integrations", "lowPrice": "As low as $109 per app", "volumeDiscounts": "Volume discounts available", "unlimitedIntegrations": "Unlimited Integrations", "prioritySupport": "Priority Support", "dedicatedManager": "Dedicated Account Manager"}, "getSingleApp": "Get Single App", "buyMaxPackage": "Buy MAX Package", "getUnlimitedAccess": "Get Unlimited Access", "bestValue": "BEST VALUE"}, "comparison": {"badge": "Feature Comparison", "title": "Compare Plans", "subtitle": "Select the perfect plan for your business needs and start building your iOS & Android app today", "moreCategories": "more categories", "showAll": "Show all features", "showLess": "Show less features", "feature": "Feature", "saveBig": "SAVE BIG", "howMuchYouSave": "How Much You Save With FluxBuilder", "description": "See how our one-time lifetime deal compares to the subscription costs of other app building platforms over 3 years.", "platform": "Platform", "pricingModel": "Pricing Model", "monthlyCost": "Monthly Cost", "threeYearCost": "3-Year Cost", "yourSavings": "Your Savings", "fluxbuilderLifetime": "FluxBuilder Lifetime", "oneTimePurchase": "One-time purchase", "oneTime": "one-time", "subscription": "Subscription", "month": "month", "save": "Save", "lifetimeValueTitle": "Lifetime Value That Keeps Growing", "lifetimeValueDescription": "With a one-time payment of just $199, your savings increase the longer you use FluxBuilder. After 3 years, you'll save at least $517 compared to the most affordable alternative, and over $17,000 compared to premium platforms!", "sourceCodeRegular": "Source Code Regular", "sourceCodeExtended": "Source Code Extended", "payGrow": "Agency Pay As You Grow", "diamond": "Agency Diamond", "categories": {"platformCompatible": "Platform Compatible", "integration": "Integration", "businessDomain": "Business Domain", "mobileAppFeatures": "Mobile App Features", "eCommerceAppFeatures": "E-Commerce App Features", "paymentMethods": "Payment Methods", "designFeatures": "Design Features", "buildAndSupport": "Build & Support", "agency": "Agency"}, "features": {"wooCommerce": "WooCommerce", "shopify": "Shopify", "magento": "Magento", "prestashop": "Prestashop", "opencart": "Opencart", "bigCommerce": "BigCommerce", "notion": "Notion", "strapi": "<PERSON><PERSON><PERSON>", "openAI": "Open AI", "ownHostingServer": "Your own hosting server", "activeDomainsNumber": "Number of Active Domains", "oneSignalNotification": "OneSignal Notification", "firebasePushNotification": "Firebase Push Notification", "firebaseDynamicLink": "Firebase Dynamic Link", "firebaseRealtimeChat": "Firebase Realtime Chat", "googleAnalytic": "Google Analytics", "googleAds": "Google Ads", "smsOTPLogin": "SMS OTP Login", "googleMapGeoLocation": "Google Maps & Geo Location", "socialLogins": "Social Logins", "appleLogin": "Apple Login", "biometricsLogin": "Biometrics Login", "instagram": "Instagram", "fullEcommerceApp": "Full E-commerce App", "buildDeliveryApp": "Build Delivery App", "buildManagerApp": "Build Manager App", "buildMarketplaceApp": "Build Marketplace App", "buildListingApp": "Build Listing App", "buildWordpressBlogApp": "Build WordPress Blog/Showcase App", "multiLanguage": "Multi-language (+50 native & RTL)", "videoOnBanner": "Video on Banner Image Slider", "remapCategories": "Remap Categories", "testimonial": "Testimonial", "wishlist": "Wishlist", "ratingMyApp": "Rating My App", "accountDeletion": "Account Deletion", "versionUpdateAlert": "Version Update Alert", "ageRestrictions": "Age Restrictions", "gdprCompliance": "GDPR Compliance", "smartChat": "Smart Chat", "darkTheme": "Dark Theme", "variantsProduct": "Variants Product", "groupProduct": "Group Product", "downloadableProduct": "Downloadable Product", "brandsCarousel": "Brands Carousel", "productImageOptions": "Product Image Options", "deliveryDatePicker": "Delivery Date Picker", "searchNearbyStores": "Search Nearby Stores", "autoSlidingProducts": "Auto-sliding on Products/Blog", "affiliatedProduct": "Affiliated Product", "billingAddressManagement": "Billing Address Management", "photoReviews": "Photo Reviews", "reviewRatingProducts": "Review/Rating Products", "shoppingVideoScrolling": "Shopping Video Scrolling", "productsGridList": "Products Grid/List", "demoOnly": "Demo Only", "oneDomain": "1 Domain"}}, "cta": {"title": "Transform Your Ideas into Mobile Apps Today", "subtitle": "Join thousands of successful businesses that have launched their mobile apps with FluxBuilder. No coding required, just your creativity.", "startBuilding": "Start Building For Free", "viewShowcase": "View App Showcase"}, "testimonials": {"sectionTitle": "Customer Success Stories", "mainTitle": "What Our Lifetime Users Say", "subtitle": "Join thousands of satisfied customers who have invested in our lifetime plans", "averageRating": "4.9/5 average rating from over 1,000+ lifetime customers", "prevTestimonial": "Previous testimonial", "nextTestimonial": "Next testimonial", "featured": [{"quote": "The lifetime plan was the best investment I made for my business. I've been using FluxBuilder for over a year now, and the ROI has been incredible.", "author": "<PERSON>", "role": "Founder", "company": "EcoMarket"}, {"quote": "I was hesitant about the upfront cost, but the lifetime plan has saved me thousands in subscription fees. The app quality is outstanding, and my customers love it.", "author": "<PERSON>", "role": "CEO", "company": "TechStart Inc."}], "regular": [{"quote": "As an agency owner, the MAX lifetime plan was a no-brainer. We've built multiple apps for our clients, and the bulk discount made it extremely cost-effective.", "author": "<PERSON>", "role": "Agency Owner", "company": "AppCraft Studio"}, {"quote": "The unlimited lifetime plan transformed our agency's business model. We now offer mobile app development as a core service without worrying about recurring costs.", "author": "<PERSON>", "role": "Marketing Director", "company": "Digital Nomads"}]}}, "megaMenu": {"categories": {"integration": "Integration", "integrationDesc": "Connect with various platforms and services", "design": "Design", "designDesc": "Design beautiful and responsive mobile apps", "features": "Features", "featuresDesc": "Core features and functionality", "platforms": "Platforms", "platformsDesc": "Build for multiple platforms and app stores", "businessCategories": "Business Categories", "businessCategoriesDesc": "Explore apps by business category", "documentation": "Documentation", "documentationDesc": "Learn how to use FluxBuilder", "community": "Community", "communityDesc": "Join the FluxBuilder community", "support": "Support", "supportDesc": "Get help with FluxBuilder"}, "labels": {"new": "New", "hot": "Hot"}, "features": {"chatGPT": "ChatGPT", "ios": "iOS", "android": "Android", "wooCommerce": "WooCommerce", "shopify": "Shopify", "wordpress": "WordPress", "magento": "Magento", "shopifyDesc": "Native Shopify support", "magentoDesc": "Magento e-commerce integration", "prestashop": "Prestashop", "prestashopDesc": "PrestaShop store support", "opencart": "Opencart", "opencartDesc": "OpenCart integration", "bigCommerce": "BigCommerce", "bigCommerceDesc": "BigCommerce store support", "webToApp": "Web To App", "webToAppDesc": "Convert any website to app", "fluxEditor": "Flux Editor 2.0", "fluxEditorDesc": "Advanced app customization tool", "smartBanner": "Smart Banner", "smartBannerDesc": "Intelligent promotional banners", "dragDrop": "Drag-and-Drop App Features", "dragDropDesc": "Easy feature customization", "templates": "Customizable Templates", "templatesDesc": "Professional app templates", "unlimitedLayout": "Unlimited Layout UI", "unlimitedLayoutDesc": "Flexible UI customization", "storySlider": "<PERSON> Slider", "storySliderDesc": "Engaging story features", "shoppingVideo": "Shopping Video", "shoppingVideoDesc": "Video shopping experience", "inAppUpdate": "In App Update", "inAppUpdateDesc": "Seamless app updates", "multilingual": "Multilingual Support", "multilingualDesc": "Multiple language support", "multiPayment": "Multi Payment", "multiPaymentDesc": "Multiple payment options", "ecommerceApp": "E-commerce App Building", "ecommerceAppDesc": "Build powerful e-commerce apps", "firebasePush": "Firebase Push Notifications", "firebasePushDesc": "Real-time notifications", "oneSignal": "OneSignal Push Notifications", "oneSignalDesc": "Advanced notification system", "smartChat": "Smart Chat", "smartChatDesc": "Intelligent chat features", "firebaseRemote": "Firebase Remote", "firebaseRemoteDesc": "Remote configuration", "buildOnCloud": "Build On Cloud", "buildOnCloudDesc": "Cloud-based app building", "darkTheme": "Dark Theme", "darkThemeDesc": "Support for dark mode UI", "customFonts": "Custom Fonts", "customFontsDesc": "Use any font in your app", "responsiveDesign": "Responsive Design", "responsiveDesignDesc": "Adapt to any screen size", "iosDesc": "Native iOS app development", "androidDesc": "Native Android app development", "pwa": "Progressive Web Apps", "pwaDesc": "Web apps with native-like features", "crossPlatform": "Cross-Platform", "crossPlatformDesc": "Build once, deploy everywhere", "appStores": "App Store Publishing", "appStoresDesc": "Publish to Apple & Google stores", "allFeatures": "All Features", "allFeaturesDesc": "Explore all available features"}, "resources": {"gettingStarted": "Getting Started", "gettingStartedDesc": "Begin your app building journey", "supportPlugins": "Support Plugins", "supportPluginsDesc": "Extend app functionality", "changelog": "Changelog", "changelogDesc": "Latest updates and changes", "blog": "Blog", "blogDesc": "Latest news and updates", "affiliate": "Affiliate", "affiliateDesc": "Join our affiliate program", "roadmap": "Roadmap", "roadmapDesc": "Future development plans", "faq": "FAQ", "faqDesc": "Frequently asked questions", "ticketSupport": "Ticket Support", "ticketSupportDesc": "Get technical support", "privacyPolicy": "Privacy Policy", "privacyPolicyDesc": "Privacy and data protection", "contact": "Contact", "contactDesc": "Get in touch with us", "aboutUs": "About Us"}, "showcase": {"shopping": "Shopping", "foodAndDrink": "Food & Drink", "lifestyle": "Lifestyle", "business": "Business", "healthAndFitness": "Health & Fitness", "education": "Education", "productivity": "Productivity", "allCategories": "All Categories", "featured": "Featured", "latestUpdates": "Latest Updates"}, "businessCategories": {"title": "Business Categories", "description": "Explore apps by business type", "items": {"shopping": {"title": "Shopping", "description": "E-commerce and retail applications"}, "foodAndDrink": {"title": "Food & Drink", "description": "Restaurant and delivery apps"}, "lifestyle": {"title": "Lifestyle", "description": "Fashion, beauty, and personal care"}, "business": {"title": "Business", "description": "Business management and productivity"}, "healthAndFitness": {"title": "Health & Fitness", "description": "Wellness and fitness tracking"}, "education": {"title": "Education", "description": "Learning and educational content"}, "productivity": {"title": "Productivity", "description": "Task management and organization"}, "allCategories": {"title": "All Categories", "description": "Browse all app categories"}}}}, "footer": {"product": "Product", "showcase": "Showcase", "fluxEditor": "Flux Editor 2.0", "chatGPTIntegration": "ChatGPT Integration", "wooCommerce": "WooCommerce", "wordpress": "Wordpress", "shopify": "Shopify", "webToApp": "Web To App", "buildOnCloud": "Build On Cloud", "allFeatures": "All Features", "helpAndResources": "Help and Resources", "gettingStarted": "Getting Started", "supportPlugins": "Support Plugins", "changelog": "Changelog", "blog": "Blog", "affiliate": "Affiliate", "roadmap": "Roadmap", "faq": "FAQ", "ticketSupport": "Ticket Support", "company": "Company", "aboutUs": "About Us", "agency": "Agency", "pricing": "Pricing", "contact": "Contact", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>", "termsOfService": "Terms of Service", "downloadDesktop": "Download FluxBuilder Desktop", "macos": "macOS", "windows": "Windows", "hot": "Hot", "new": "New", "showMore": "Show more", "showLess": "Show less", "keyBenefits": "Key Benefits", "securityPrivacy": "Security & Privacy Practices", "description": "FluxBuilder automates your mobile app creation — handling design, development, and publishing. Try it for free starting today!"}, "features": {"pageTitle": "Features", "pageDescription": "Explore all the powerful features of FluxBuilder that let you create mobile apps without writing a single line of code.", "heading": "Complete Feature Set", "subheading": "FluxBuilder provides comprehensive features to help you create professional, engaging mobile apps without any technical expertise.", "searchPlaceholder": "Search features (e.g., Push Notifications, Chat...)", "categoriesHeading": "Feature Categories", "noFeaturesFound": "No features found", "tryAdjusting": "Try adjusting your search or filter criteria", "resetFilters": "Reset filters", "clearSearch": "Clear search", "learnMore": "Learn more about this feature", "allFeatures": "All Features", "searchFeatures": "Search features...", "selectCategory": "Select category: ", "allCategories": "All Categories", "tryAdjustingSearch": "Try adjusting your search terms or browse by category", "categories": {"all": "All", "core": "Core", "integration": "Integration", "payment": "Payment", "ecommerce": "E-commerce", "marketplace": "Marketplace", "management": "Management & Analytics", "agency": "Agency"}}, "home": {"hero": {"title": "Transform Your Website Into a", "titleHighlight": "Native Mobile App", "subtitle": "FluxBuilder automates your mobile app creation — handling design, development, and publishing. Try it for free starting today!", "enterWebsite": "Enter your website URL", "tryItNow": "Try It Now", "orDownload": "or download FluxBuilder", "trustedBy": "Trusted by 50,000+ users", "worksWithAllPlatforms": "Works with all major platforms", "noCreditCard": "No credit card required", "instantPreview": "Instant preview", "iosAndroidSupport": "iOS & Android support", "userCount": "50,000+ users"}, "features": {"title": "Features That Make FluxBuilder Stand Out", "subtitle": "Everything you need to create a professional mobile app from your website", "viewAllFeatures": "View All Features", "badges": {"new": "New", "popular": "Popular", "premium": "Premium", "essential": "Essential", "fast": "Fast", "timeSaver": "Time Saver"}, "items": {"quickSetup": {"title": "Quick Setup", "description": "Get your app up and running in minutes with our intuitive setup process. No technical knowledge required."}, "stunningDesign": {"title": "Stunning Design", "description": "Create beautiful, professional-looking apps with our pre-designed templates and customization options."}, "advancedFeatures": {"title": "Advanced Features", "description": "Access powerful features like push notifications, in-app purchases, and user authentication."}, "multiPlatform": {"title": "Multi-Platform", "description": "Build once and deploy to both iOS and Android platforms, reaching all your potential users."}, "performanceOptimized": {"title": "Performance Optimized", "description": "Enjoy fast loading times and smooth performance with our optimized Flutter-based architecture."}, "seamlessUpdates": {"title": "Seamless Updates", "description": "Update your app content and features instantly without requiring app store approvals."}}}, "howItWorks": {"title": "How FluxBuilder Works", "subtitle": "Simple 3-step process to create your mobile app", "step1": "Step 1", "step2": "Step 2", "step3": "Step 3", "startNow": "Start Now", "noCreditCard": "Free version available. No credit card required.", "steps": {"connect": {"title": "Connect Your Website", "description": "Start by connecting your existing website. FluxBuilder works with most platforms including WordPress, Shopify, WooCommerce and custom sites."}, "customize": {"title": "Customize Your App", "description": "Use our drag & drop builder to customize the look and feel of your app without writing any code."}, "publish": {"title": "Publish to App Stores", "description": "We handle the technical aspects of publishing your app to both Apple App Store and Google Play Store."}}}, "cta": {"title": "Ready to Transform Your Website?", "badge": "Get started today", "mainTitle": "Launch Your Mobile App in Minutes, Not Months", "subtitle": "Join thousands of businesses that have already created their mobile apps with FluxBuilder", "button": "Get Started For Free", "happyCustomers": "Happy Customers", "customerRating": "Customer Rating", "setupTime": "Average Setup Time", "noCreditCard": "No credit card required", "freeVersion": "Free version available", "instantPreview": "Instant app preview", "support": "24/7 customer support", "testimonial": "FluxBuilder has been a game-changer for our business. We were able to launch our mobile app in days, not months, and the results have exceeded our expectations.", "testimonialAuthor": "<PERSON>", "testimonialPosition": "CEO, TechSolutions"}, "whyFluxBuilder": {"title": "Why use FluxBuilder?", "subtitle": "See how FluxBuilder stands out from other app builders with professional features and unlimited possibilities.", "tableHeader": {"features": "Features", "others": "Other App Builders", "fluxBuilder": "FluxBuilder"}, "features": {"sourceCode": "Include Source code to unlock all customization", "whiteLabel": "Support Agency white-label and Dashboard", "unlimitedBuild": "Unlimited Build On Cloud", "longTermSupport": "Long term support and maintain", "productionReady": "Build completely production app ready, not just the UI", "nativePerformance": "True native performance with Flutter framework"}, "cta": {"text": "Ready to experience the FluxBuilder difference?", "download": "Download FluxBuilder", "features": "View All Features"}, "benefits": {"fast": {"title": "Lightning Fast", "description": "Build and deploy your app in minutes, not months"}, "professional": {"title": "Professional Grade", "description": "Enterprise-level features and security standards"}, "support": {"title": "Dedicated Support", "description": "24/7 expert support and comprehensive documentation"}}}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about FluxBuilder and creating your mobile app", "commonQuestions": "Common Questions", "stillHaveQuestions": "Still have questions? We're here to help!", "contactSupport": "Contact Support", "items": {"whatIsFluxBuilder": {"question": "What is FluxBuilder and how does it work?", "answer": "FluxBuilder is a powerful no-code mobile app builder that lets you create professional iOS and Android apps without any coding knowledge. It works through an intuitive drag-and-drop interface that makes app creation accessible to everyone. With FluxBuilder, you can design your app's UI, configure features, connect to various platforms (like WooCommerce, Shopify, etc.), and publish directly to app stores—all from a single dashboard."}, "platforms": {"question": "What platforms can I integrate with FluxBuilder?", "answer": "FluxBuilder seamlessly integrates with all major e-commerce and content platforms, including WooCommerce, Shopify, Magento, OpenCart, PrestaShop, Strapi, WordPress, and Custom APIs. This flexibility allows you to connect your existing business infrastructure with your mobile app without any complex development work."}, "paymentGateways": {"question": "What payment gateways does FluxBuilder support?", "answer": "FluxBuilder supports a wide range of payment gateways to ensure your customers can pay however they prefer, including PayPal, Stripe, Apple Pay, Google Pay, Razorpay, Braintree, Cash on Delivery, and many more platform-specific payment options. You can enable multiple payment methods to provide flexibility for your customers and increase conversion rates."}, "dragAndDrop": {"question": "How does the drag-and-drop builder work?", "answer": "FluxBuilder's drag-and-drop interface makes app creation intuitive and visual. You can drag UI components (buttons, images, text fields, etc.) onto your app screens, arrange elements by simply dragging them to the desired position, customize each element's appearance through a visual properties panel, preview changes in real-time with our live preview feature, and create complex layouts without writing a single line of code. The builder follows a 'what you see is what you get' approach, so your app will look exactly as you design it."}, "multilingual": {"question": "Does FluxBuilder support multiple languages?", "answer": "Yes! FluxBuilder offers comprehensive multilingual support with over 50 languages available out of the box. You can create apps in any language, allow users to switch between languages within your app, support right-to-left (RTL) languages like Arabic and Hebrew, customize translations for any text in your app, and auto-detect the user's device language. This makes FluxBuilder perfect for businesses with a global audience or those operating in multilingual regions."}, "analytics": {"question": "What analytics and tracking features are available?", "answer": "FluxBuilder includes a comprehensive analytics dashboard that helps you understand user behavior and optimize your app. Key features include user engagement metrics (active users, session duration, retention), screen view tracking to see which parts of your app are most popular, conversion tracking for e-commerce apps, custom event tracking, user demographic information, and integration with Google Analytics and Firebase Analytics. These insights help you make data-driven decisions to improve your app and increase user satisfaction."}, "whiteLabel": {"question": "Can I white-label my app created with FluxBuilder?", "answer": "Yes! FluxBuilder's white-labeling capabilities allow you to create fully branded apps with no mention of FluxBuilder. You can use your own logo, brand colors, and identity throughout the app, customize the app icon, splash screen, and loading animations, publish under your own developer account on app stores, and create a completely custom user experience that matches your brand. This is especially valuable for agencies and businesses that want to maintain a consistent brand experience across all customer touchpoints."}}}, "integration": {"title": "Works With All Major Platforms", "subtitle": "FluxBuilder integrates seamlessly with your existing website"}, "trust": {"title": "Transform Your Agency's Performance", "subtitle": "See what our customers are saying about FluxBuilder", "appsPublished": "Apps Published", "starReviews": "5-Star Reviews", "verifiedReviews": "Verified Reviews", "verifiedReviewsLabel": "Verified Customer Reviews", "expertSupport": "Expert Support", "viewShowcase": "View Success Apps", "reviews": {"review1": "We've been using FluxBuilder for a year now, and it's been a fantastic experience. The app makes it incredibly easy to build mobile applications for iOS and Android. The customer service is exceptional - when issues arise, new updates provide quick and effective solutions.\n\nThanks to FluxBuilder, our dream of launching an app has become a reality! Our customers love the seamless experience, and the app is very easy to use. We highly recommend FluxBuilder - 100% satisfied!", "review2": "We worked with Inspire UI to build a custom Membership Pricing feature, and the experience was outstanding. From the start, their team demonstrated a deep understanding of our requirements and delivered a clean, efficient, and scalable solution.\n\nThey were quick to respond, open to feedback, and always professional in their communication. The membership feature integrates seamlessly into our platform, offering flexibility and a smooth user experience for our customers.\n\nIf you're looking for a reliable and skilled team to bring your ideas to life - especially in Flutter or Shopify integrations - Inspire UI is the partner you can trust.\n\nHighly recommended!", "review3": "It's really a great app, I had concerns at first. It's really great that it's available on every platform and has a flexible and useful interface. I was very deficient in flutter and dart when it came to writing code, but this application has compensated for all my shortcomings.", "review4": "After wasting time trying to solve the problem, I contacted inspireui.com support as a last resort. And that was the right decision. Their support is fast, concrete, and professional. Together we solved the problem, and now I can move forward. I give all praise and recommendations to the Inspireui.com team because they really know how to support the user. Add great apps and a platform to that, and you have a perfect, complete solution for your mobile apps. Keep it up!", "review5": "Inspire UI has reinvented the way of developing apps by giving everyone the ability to create one, with little effort and little development knowledge. They always introduce many new features in their tools, but above all they are always available and courteous in answering questions and solving any problems.", "review6": "They are a professional team and supported me patiently", "review7": "Great support, extremely clear and communicative. Problem solved thanks to them.", "review8": "I would like to express my sincere gratitude to inspire u<PERSON> for the exceptional support they provided. They went above and beyond to help me, demonstrating a high level of commitment to providing excellent service. The team was professional, friendly, and made my experience smooth and enjoyable. Thank you to all - you are undoubtedly the best", "review9": "I've been using Fluxbuilder for 2 weeks and I'm quite impressed with how easy it is to build an app. But the reason I wanted to give a 5-star review is the excellent support this company has. I got a problem solved immediately, thanks to the FluxBuilder team for that."}, "readMore": "Read more", "businessOwner": "Business Owner", "developer": "Developer", "ecommerceBusiness": "E-commerce Business"}, "showcase": {"title": "App Showcase", "subtitle": "See real-world examples of mobile apps built and launched with FluxBuilder", "noScreenshots": "No screenshots available", "updated": "Updated", "updatedOn": "Updated on", "screenshot": "Screenshot", "images": "images", "reviews": "Reviews", "installs": "Installs", "appStore": "App Store", "googlePlay": "Google Play", "viewAll": "View All Apps", "ctaText": "Let FluxBuilder help you bring your creative ideas to life. Sign up today and start creating beautiful app.", "loadingMessage": "Please wait while we load the showcase data...", "noItemsFound": "No Showcase Items Found", "noItemsMessage": "We couldn't find any showcase items. Please try again later.", "noItemsInCategory": "No apps found in this category", "found": "Found", "app": "app", "apps": "apps", "in": "in", "for": "for", "with": "with", "updatedInLast": "updated in the last", "days": "days", "downloads": {"1k": "1K+ Downloads", "10k": "10K+ Downloads", "100k": "100K+ Downloads"}, "timeFilters": {"latest": "Latest Updates", "30days": "Last 30 Days", "60days": "Last 60 Days", "featured": "Featured"}}}, "cookieConsent": {"message": "We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic. By clicking \"Accept\", you consent to our use of cookies.", "accept": "Accept", "decline": "Decline"}, "privacy": {"pageTitle": "Privacy Policy | FluxBuilder - App Builder Without Code", "pageDescription": "Read FluxBuilder's privacy policy to understand how we collect, use, and protect your personal information.", "title": "Privacy Policy", "subtitle": "Your privacy is important to us. This policy outlines how we collect, use, and protect your information.", "lastUpdated": "Last Updated: May 1, 2023"}, "cookies": {"pageTitle": "Cookie Policy | FluxBuilder - App Builder Without Code", "pageDescription": "Read FluxBuilder's cookie policy to understand how we use cookies and similar technologies on our website and mobile applications.", "title": "<PERSON><PERSON>", "subtitle": "This policy explains how we use cookies and similar technologies to enhance your browsing experience.", "lastUpdated": "Last Updated: May 1, 2023", "sections": {"introduction": {"title": "Introduction", "content": ["This Cookie Policy explains how FluxBuilder uses cookies and similar technologies to recognize you when you visit our website and mobile applications (collectively, the \"Services\"). It explains what these technologies are and why we use them, as well as your rights to control our use of them.", "By continuing to use our Services, you are agreeing to our use of cookies as described in this Cookie Policy."]}, "whatAreCookies": {"title": "What Are Cookies", "content": ["Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners to make their websites work, or to work more efficiently, as well as to provide reporting information.", "Cookies set by the website owner (in this case, FluxBuilder) are called \"first-party cookies\". Cookies set by parties other than the website owner are called \"third-party cookies\". Third-party cookies enable third-party features or functionality to be provided on or through the website (e.g., advertising, interactive content, and analytics). The parties that set these third-party cookies can recognize your computer both when it visits the website in question and also when it visits certain other websites."]}, "whyWeUseCookies": {"title": "Why We Use Cookies", "content": ["We use first-party and third-party cookies for several reasons. Some cookies are required for technical reasons in order for our Services to operate, and we refer to these as \"essential\" or \"strictly necessary\" cookies. Other cookies also enable us to track and target the interests of our users to enhance the experience on our Services. Third parties serve cookies through our Services for advertising, analytics, and other purposes. This is described in more detail below.", "The specific types of first and third-party cookies served through our Services and the purposes they perform are described in the table below:"]}, "typesOfCookies": {"title": "Types of Cookies We Use", "content": ["• Essential Cookies: These cookies are strictly necessary to provide you with services available through our Services and to use some of its features, such as access to secure areas. Because these cookies are strictly necessary to deliver the Services, you cannot refuse them without impacting how our Services function.", "• Performance and Functionality Cookies: These cookies are used to enhance the performance and functionality of our Services but are non-essential to their use. However, without these cookies, certain functionality may become unavailable.", "• Analytics and Customization Cookies: These cookies collect information that is used either in aggregate form to help us understand how our Services are being used or how effective our marketing campaigns are, or to help us customize our Services for you in order to enhance your experience.", "• Advertising Cookies: These cookies are used to make advertising messages more relevant to you and your interests. They also perform functions like preventing the same ad from continuously reappearing, ensuring that ads are properly displayed, and in some cases selecting advertisements that are based on your interests.", "• Social Media Cookies: These cookies are used to enable you to share pages and content that you find interesting on our Services through third-party social networking and other websites. These cookies may also be used for advertising purposes."]}, "howToControlCookies": {"title": "How Can You Control Cookies", "content": ["You have the right to decide whether to accept or reject cookies. You can exercise your cookie preferences by clicking on the appropriate opt-out links provided in the cookie table above.", "You can set or amend your web browser controls to accept or refuse cookies. If you choose to reject cookies, you may still use our Services though your access to some functionality and areas of our Services may be restricted. As the means by which you can refuse cookies through your web browser controls vary from browser-to-browser, you should visit your browser's help menu for more information.", "In addition, most advertising networks offer you a way to opt out of targeted advertising. If you would like to find out more information, please visit http://www.aboutads.info/choices/ or http://www.youronlinechoices.com.", "The specific types of first and third-party cookies served through our Services and the purposes they perform are described in the cookie consent tool that appears when you first visit our website."]}, "cookiesWeSet": {"title": "Cookies We Set", "content": ["• Session Cookies: We use Session Cookies to operate our Services.", "• Preference Cookies: We use Preference Cookies to remember your preferences and various settings.", "• Security Cookies: We use Security Cookies for security purposes.", "• Analytics Cookies: We use Google Analytics to help us understand how our visitors use the website. You can read more about how Google uses your Personal Information here: https://www.google.com/intl/en/policies/privacy/. You can also opt-out of Google Analytics here: https://tools.google.com/dlpage/gaoptout."]}, "thirdPartyCookies": {"title": "Third-Party Cookies", "content": ["In addition to our own cookies, we may also use various third-party cookies to report usage statistics of the Services, deliver advertisements on and through the Services, and so on.", "• _ga, _gid, _gat (Google Analytics): Used to distinguish users and throttle request rate.", "• _fbp (Facebook): Used by Facebook to deliver a series of advertisement products such as real-time bidding from third party advertisers.", "• _hjid (Hotjar): Used to identify a user during their first session on a site."]}, "cookieUpdates": {"title": "Updates to This <PERSON>ie Policy", "content": ["We may update this Cookie Policy from time to time in order to reflect, for example, changes to the cookies we use or for other operational, legal, or regulatory reasons. Please therefore re-visit this Cookie Policy regularly to stay informed about our use of cookies and related technologies.", "The date at the top of this Cookie Policy indicates when it was last updated."]}, "contactUs": {"title": "Contact Us", "content": ["If you have any questions about our use of cookies or other technologies, please contact us at:", "FluxBuilder, Inc.", "123 Privacy Street", "San Francisco, CA 94107", "Email: <EMAIL>", "Phone: (*************"]}}}, "agency": {"meta": {"title": "Mobile App Builder for Agencies | White-Label App Solutions | FluxBuilder", "description": "Turn your agency into a profitable app business. Create & sell custom iOS & Android apps for clients without coding. Increase revenue by 70% & deliver apps 4x faster."}, "hero": {"badge": "Agency Solutions", "title": "Create & Sell <span>Mobile Apps</span> For Your Clients — No Coding Required", "subtitle": "Turn your agency into a profitable app development business. Our white-label platform helps agencies earn $10,000+ monthly with custom mobile apps.", "features": {"brandApp": "Your Brand, Your App Builder", "platforms": "iOS & Android App Creation", "noCoding": "No Coding Skills Required", "payment": "Custom Payment Processing", "clientManagement": "Unlimited Client Management", "templates": "Ready-to-Use App Templates", "cloudPlatform": "Cloud-Based Building Platform", "customDomain": "Custom Domain & Branding", "resell": "Resell Under Your Own Pricing", "whiteLabelSolution": "Full White-Label Solution", "support": "Technical Support Included", "updates": "Regular Platform Updates"}, "buttons": {"startBuilding": "Start Building Apps Today", "contact": "Contact Us"}, "trustBadge": {"title": "Trusted by 50,000+ Users", "subtitle": "Building profitable mobile app businesses"}}, "trust": {"badge": "Agency Solutions", "title": "Trusted by Thousands of Agencies", "subtitle": "See what our customers are saying about their experience with FluxBuilder", "appsPublished": "Apps Published", "starReviews": "5-Star Reviews", "verifiedReviews": "Verified Reviews", "expertSupport": "Expert Support", "joinButton": "Join Successful Agencies Today", "noTechnicalSkills": "No technical skills required. Full support included."}, "benefits": {"badge": "Why Choose FluxBuilder", "title": "Why <span>Leading Agencies</span> Choose FluxBuilder", "subtitle": "Join thousands of successful agencies that have transformed their business with our white-label mobile app platform.", "items": [{"title": "White Label Mobile Apps", "description": "Deliver fully branded apps under your own name. Customize every aspect including colors, logos, and UI elements to perfectly match your clients' brand identity."}, {"title": "Unlimited Client Projects", "description": "Scale your agency without limits. Create and manage unlimited mobile apps for all your clients from a single dashboard, with no per-project fees or restrictions."}, {"title": "80% Faster Development", "description": "Deliver complete mobile apps in days instead of months. Our drag-and-drop builder eliminates coding time, allowing you to take on more projects and increase revenue."}, {"title": "Multi-Client Management", "description": "Organize all your client projects with our powerful agency dashboard. Set custom permissions, collaborate with team members, and streamline your workflow."}, {"title": "Recurring Revenue Model", "description": "Transform one-time projects into monthly recurring revenue. Offer app maintenance, updates, and support packages to create predictable income streams."}, {"title": "Enterprise-Grade Features", "description": "Deliver premium solutions with our advanced features including custom API integrations, payment gateways, real-time analytics, and multi-language support."}]}, "metrics": {"badge": "Proven Results", "title": "Transform Your Agency's <span>Revenue</span> & Performance", "subtitle": "Our partners report dramatic improvements in efficiency, profitability, and client satisfaction after implementing FluxBuilder.", "items": [{"value": "85%", "label": "Reduction in development time", "description": "Launch apps in days instead of months"}, {"value": "70%", "label": "Higher profit margins", "description": "Eliminate expensive developer costs"}, {"value": "4x", "label": "More client capacity", "description": "<PERSON>le quadruple the projects simultaneously"}, {"value": "$5K+", "label": "Monthly recurring revenue", "description": "Per client with maintenance packages"}]}, "whiteLabel": {"badge": "White Label Solution", "title": "Your <span>Brand</span>, Your App Builder — Complete White Label Solution", "subtitle": "Rebrand our powerful app builder as your own product. Add your logo, colors, and domain to create a seamless experience for your clients.", "slides": [{"title": "Complete White Label Solution", "content": "Launch your own branded app builder in minutes. Your clients will never know you're using FluxBuilder - they'll just see your brand, your domain, and your success stories."}, {"title": "Premium Features Without Technical Hassle", "content": "Offer enterprise-grade features without the technical complexity. Build iOS apps without Mac hardware, customize payment gateways, and access all VIP features under your own brand."}, {"title": "Custom Templates & Source Code Control", "content": "Create stunning app templates that set your agency apart. Use your own GitHub repository and custom source code to deliver unique solutions that perfectly match each client's brand and requirements."}, {"title": "Unlimited Growth Potential", "content": "Scale your agency without limits. Our Diamond plan lets you build unlimited apps for unlimited clients worldwide, creating a sustainable recurring revenue stream that grows with your business."}]}, "howItWorks": {"badge": "How It Works", "title": "Simple Three-Step Process to Build Client Apps", "subtitle": "FluxBuilder streamlines the entire app creation process so you can focus on delivering value to your clients.", "steps": [{"number": "01", "title": "Set Up Your Agency Dashboard", "description": "Create your branded app builder platform with your domain, logo, and colors. Configure your agency profile and add team members with custom roles and permissions."}, {"number": "02", "title": "Create Client Projects", "description": "Add client accounts, connect to their websites, and create custom app templates. Use our drag-and-drop builder to customize every aspect of the app without coding."}, {"number": "03", "title": "Publish & Generate Revenue", "description": "Publish apps to app stores under your own brand and set up recurring maintenance packages. Leverage our white-label documentation to provide ongoing support to your clients."}]}, "templates": {"badge": "Template Library", "title": "Create Once, <span>Profit</span> Repeatedly", "subtitle": "Build your template library and monetize it. Create beautiful app designs once and resell them to multiple clients, maximizing your ROI with minimal effort.", "features": ["Create unlimited custom templates", "Share templates with specific clients", "Organize templates by category", "Update templates in real-time", "Import/export template designs"]}, "management": {"badge": "Client Dashboard", "title": "Scale Your Agency with <span>Unlimited</span> Clients", "subtitle": "Our powerful client management system helps you handle more clients with less effort. Automate billing, track projects, and manage permissions all from one dashboard.", "features": ["Centralized client dashboard", "Custom access permissions", "Detailed analytics and reporting", "Automated billing and invoicing", "Client communication tools"]}, "pricing": {"badge": "Agency Solutions", "title": "Transparent <span>Pricing</span> Plans", "subtitle": "Choose the perfect plan to start building and selling mobile apps under your own brand", "plans": {"dynamic": {"badge": "POPULAR", "title": "Pay As You Grow", "subtitle": "Start small, scale big with flexible pricing", "price": "$99", "originalPrice": "$200", "period": "/month", "promo": {"label": "Promo Code:", "code": "AGENCY2025"}, "variablePrice": "+ <strong>$25</strong> * the number of built apps", "buttonText": "Start Your White-Label Business", "featuresTitle": "Plan includes:"}, "diamond": {"badge": "ENTERPRISE", "title": "Diamond 💎", "subtitle": "Enterprise-grade power for serious growth", "price": "Custom Pricing", "description": "Tailored pricing based on your agency size and specific business needs", "buttonText": "Get Custom Quote", "featuresTitle": "Everything in Dynamic plan, plus:", "features": ["Custom UI template library", "Your own GitHub repository integration", "Premium UI design templates", "Priority access to new features", "Dedicated account manager", "Unlimited app creation"]}}, "guarantee": "Risk-free 30-day money-back guarantee"}, "pricingCta": {"badge": "Limited Time Offer", "title": "Ready to <span>Transform</span> Your Agency?", "subtitle": "Get started with FluxBuilder Agency today and receive a <strong>50% discount</strong> on your first 12 months with promo code <code>AGENCY2025</code>", "features": [{"title": "White Label Solution", "description": "Fully customizable with your branding"}, {"title": "Custom Domain", "description": "Use your own domain for your app builder"}, {"title": "Unlimited Customers", "description": "Scale your business without limits"}, {"title": "VIP Features", "description": "Access to all premium features"}, {"title": "Admin Dashboard Control", "description": "Complete admin panel to manage all client projects"}, {"title": "No Mac Required for iOS", "description": "Build iOS apps without owning Apple hardware"}], "buttons": {"claim": "Claim Your 50% Discount", "demo": "Schedule a Demo"}, "contactInfo": "Questions? Email us at <email><EMAIL></email> or WhatsApp <whatsapp>+84 9899 088 54</whatsapp>"}}, "about": {"meta": {"title": "About Us - InspireUI", "metaTitle": "About InspireUI | Our Mission & Services", "description": "Learn about InspireUI's mission, our commitment to excellence, and the services we offer to help you build amazing mobile apps.", "keywords": "InspireUI about, mobile app builder company, app development services, InspireUI, app customization, app design services", "structuredData": {"description": "A leading production company offering top-notch products and services for exceptional Mobile Application development, design, and prototypes."}}, "hero": {"title": {"1": "Unleash Your Creative Potential with", "2": "InspireUI Ltd"}, "subtitle": "A leading production company offering top-notch products and services for exceptional Mobile Application development, design, and prototypes."}, "mission": {"title": "Our Commitment to Excellence", "items": {"1": {"title": "Gratitude to Our Customers", "content": "We are deeply grateful to our valued customers who have trusted InspireUI for their app development needs. Your success stories inspire us to push boundaries and deliver exceptional products."}, "2": {"title": "Continuous Improvement", "content": "We are committed to regular updates and improvements to ensure InspireUI remains at the forefront of app development technology. Our team works tirelessly to enhance features and maintain the highest standards of quality."}, "3": {"title": "Built with Love", "content": "Every line of code in InspireUI is written with passion and dedication. Our team pours their heart into creating a product that empowers developers and businesses to achieve their goals."}}}, "stats": {"1": {"label": "Happy Customers", "value": "50,000+"}, "2": {"label": "Countries represented", "value": "140"}, "3": {"label": "5-Star Reviews", "value": "1000+ ⭐️"}, "4": {"label": "Success Apps on Stores", "value": "19,000+"}}, "testimonials": {"1": {"content": "I've been using their products for some time and built 3 Apps based on FluxStore. It gives an excellent platform that can be extended if you have coding knowledge. The Support is extremely helpful and gives insightful tips on how to overcome challenges.", "author": "<PERSON>", "country": "Portugal"}, "2": {"content": "InspireUI is great for people without coding experience. The support is quick and efficient. It is easy to get shoot a ticket or give feedback about the functionalities. I'm currently in the process of submitting the first app to the PlayStore.", "author": "<PERSON>", "country": "Netherlands"}, "3": {"content": "The whole experience with inspire UI has been amazing. The product is outstanding and very easy to use, the guidelines are super complete and coherent. And they have given me the best support and customer service. Thumbs up!!", "author": "An", "country": "Colombia"}}, "services": {"title": "Our Services", "below50Hours": "Below 50 hours", "above50Hours": "Above 50 hours", "1": {"title": "Installing Service", "description": "Delivery service to support publish the app to the Appstore or Google Play Store", "price": {"ios": "$1000", "both": "$1600"}}, "2": {"title": "Customization App", "description": "We provide support for Flutter projects after purchasing the Extended License or Agency plan.", "price": {"below50": "$50/hour", "above50": "$40/hour"}}, "3": {"title": "Design Service", "description": "Elevate your brand with exceptional design. Our team of creative experts crafts stunning visuals that captivate your audience and drive results.", "price": "Contact Us"}}, "contact": {"title": "Looking for a bigger commitment?", "subtitle": "Let's talk and we'll bring something great for you", "buttonText": "Contact Us"}, "meta_title": "About Us - InspireUI", "meta_description": "Learn about InspireUI, a design-first platform empowering anyone to create exceptional mobile apps without coding.", "title": "About Us", "heading": "About Us", "build_title": "Build Without Limits", "build_description1": "InspireUI is a design-first platform that empowers anyone to create exceptional mobile apps without writing a single line of code.", "build_description2": "Our mission is to make app development accessible and enjoyable for everyone, providing the tools and resources to turn ideas into reality.", "alt_team_work": "Team collaboration at InspireUI", "alt_woman_working": "Woman developing mobile app with InspireUI", "stats_heading1": "1 team.", "stats_heading2": "5+ years.", "stats_heading3": "26K+ licenses.", "stats_description": "Leading the mobile app market since 2018, InspireUI offers the highest quality templates and has generated over $1.5 million in revenue.", "client_logo": "Client logo", "testimonial_person": "<PERSON>, customer from Canada", "five_stars": "5 star rating", "testimonial_name": "<PERSON>", "testimonial_role": "Customer from Canada", "testimonial_quote": "\"Trust Inspire UI! I've been building websites for over 20 years but needed to create a mobile app and came across Inspire UI. Using the software is relatively simple, and they continue to make it even more easy to use with recent updates...\"", "contact_heading": "Let's Build Something Amazing Together", "contact_description": "Have a question, need support, or want to discuss your next big app idea? We're here to help! Our team is dedicated to providing exceptional customer service.", "office_hours_title": "Office Hours", "office_hours_weekdays": "Monday-Friday", "office_hours_time": "8:00 am to 5:00 pm", "address_title": "Our Address", "address_line1": "SBI Building, Street 3,", "address_line2": "Quang Trung Software Park, Vietnam", "follow_us_title": "Follow us", "twitter": "Twitter", "youtube": "Youtube channel", "facebook": "Facebook Group", "contact_title": "Get In Touch", "phone": "Phone", "email": "Email", "chat_with_us": "Chat with us!"}, "team": {"power_elite_badge": "Power Elite Author Badge", "trusted_heading": "Trusted by thousands of businesses worldwide", "envato_description": "InspireUI has sold more than $1 million dollars worth of items on the Envato Marketplaces and is the Power Elite Author", "see_more": "See more...", "happy_customers": "Happy Customers", "countries": "Countries represented", "reviews": "5-Star Reviews", "success_apps": "Success Apps on Stores", "metrics_image": "InspireUI business metrics and success statistics"}, "user_rating": {"user_photo": "%s's profile photo", "location_pin": "Location pin", "five_stars": "5 star rating", "customers_say": "Our customers say", "excellent": "Excellent", "rating_text": "4.6 out of five star rating on Trustpilot", "feedback1": "I've been using their products for some time and built 3 Apps based on FluxStore. It gives an excellent platform that can be extended if you have coding knowledge. The Support is extremely helpful and gives insightful tips on how to overcome challenges.", "feedback2": "InspireUI is great for people without coding experience. The support is quick and efficient. It is easy to get shoot a ticket or give feedback about the functionalities. I'm currently in the process of submitting the first app to the PlayStore.", "feedback3": "The whole experience with inspire UI has been amazing. The product is outstanding and very easy to use, the guidelines are super complete and coherent. And they have given me the best support and customer service. Thumbs up!!", "feedback4": "InspireUI is a company that provides Multi Platform Mobile app. They also have a number of products codecanyon and themeforest. InspireUI has a positive reputation for its products and services, and they have been praised for their high quality and customer support.", "feedback5": "Generally speaking, builder flutter apps is a bit challenging yet highly rewarding. If it was not for the great UI and excellent customer support that inspireui extended to me, my mission of building my first app would have been much more difficult. Thanks, guys, you were great!!! HIGHLY RECOMMENDED!", "feedback6": "it's perfect and all support team help us for all our requirements and our issues, thanks a lot team for help, really happy working with inspireui :)", "portugal": "Portugal", "netherlands": "Netherlands", "colombia": "Colombia", "qatar": "Qatar", "czech": "Czech", "uae": "U.A.E."}, "download": {"meta": {"title": "Download FluxBuilder | App Builder Without Code", "description": "Download FluxBuilder for Windows or macOS. Start building your mobile app without writing a single line of code."}, "hero": {"title": "Download", "subtitle": "Get started with FluxBuilder today and create your mobile app without writing code. Available for all major platforms."}, "options": {"title": "Choose Your Platform", "subtitle": "FluxBuilder is available for all major operating systems", "size": "Size"}, "windows": {"title": "FluxBuilder for Windows", "description": "Desktop application for Windows 10/11", "os": "Windows 10/11 (64-bit)", "buttonText": "Download for Windows", "features": {"performance": "Native Performance", "offline": "Offline Support", "updates": "Auto Updates"}}, "macos": {"title": "FluxBuilder for macOS", "description": "Desktop application for macOS", "os": "macOS 10.15 or later", "buttonText": "Download for macOS", "features": {"silicon": "Apple Silicon Ready", "universal": "Universal Binary", "touchbar": "Touch Bar Support"}}, "cloud": {"title": "FluxBuilder Cloud", "description": "No installation required, access from any browser", "size": "(Browser-based)", "os": "Any modern browser", "buttonText": "Launch Web App", "features": {"collaboration": "Real-time Collaboration", "autosave": "Auto-save", "crossplatform": "Cross-platform"}}, "steps": {"title": "Getting Started", "subtitle": "Follow these simple steps to install and start using FluxBuilder", "download": {"title": "Download the installer", "description": "Select and download the version of FluxBuilder that matches your operating system from the options above."}, "install": {"title": "Run the installer", "description": "Double-click the downloaded file to start the installation process. Follow the on-screen instructions to install FluxBuilder on your computer."}, "account": {"title": "Create your account", "description": "Launch FluxBuilder and create a new account or sign in with your existing credentials. This allows you to save your projects and access them from any device."}, "build": {"title": "Start building your app", "description": "Choose from a variety of templates or start from scratch. Use the drag-and-drop interface to design your app, add functionality, and customize to your needs."}}, "thankyou": {"title": "Thank you for downloading", "message": "Thank you for choosing FluxBuilder! We're excited to help you create amazing mobile apps. If you need any assistance, our support team is always here to help."}, "meteors": {"meta": {"title": "Meteors Effect Demo | FluxBuilder", "description": "Explore different configurations of the Meteors effect component for your web applications."}}}, "faq": {"categories": {"general": "General", "pricing": "Plans & Pricing", "privacy": "Privacy & Security"}, "general": {"whatIsFluxBuilder": {"question": "What is FluxBuilder?", "answer": "FluxBuilder is a drag-and-drop app builder that enables users to create mobile applications for iOS and Android devices without coding skills. It provides a user-friendly tool that helps businesses build Flutter mobile apps with powerful integrations, flexible design customization, and instant delivery capabilities."}, "platforms": {"question": "What platforms does FluxBuilder support?", "answer": "FluxBuilder is compatible with many famous platforms including WooCommerce, WCFM, Dokan, Shopify, OpenCart, WordPress, Listeo, MyListing, ListingPro, Magento, PrestaShop, BigCommerce, Notion, and Strapi. It runs on Windows, macOS, and has a web version."}, "appTypes": {"question": "What kind of apps can I create with FluxBuilder?", "answer": "With FluxBuilder, you can create a wide range of mobile apps including e-commerce, listing directory, news, pharmacy, grocery, marketplace, food delivery, restaurant, salon, social media, fitness apps, and many more. The platform supports multipurpose app development."}, "easyToUse": {"question": "Is FluxBuilder easy to use for non-technical users?", "answer": "Yes, FluxBuilder is specifically designed to be user-friendly with a drag-and-drop interface and intuitive controls. Non-technical users can auto-build both iOS and Android apps without any coding knowledge. The platform provides comprehensive documentation and guided walkthroughs."}, "features": {"question": "What are FluxBuilder's main features?", "answer": "FluxBuilder offers powerful integrations with top eCommerce platforms, flexible design & customization with 100+ templates, instant delivery through cloud storage, cross-platform compatibility, drag-and-drop design, multi-language support (50+ languages), Firebase integration, payment gateways, and much more."}, "support": {"question": "What kind of support does FluxBuilder offer?", "answer": "FluxBuilder offers comprehensive support including a knowledge base, community forum, support center, email support, and detailed documentation. Users can also schedule one-on-one calls with support representatives when needed."}}, "pricing": {"cost": {"question": "How much does FluxBuilder cost?", "answer": "FluxBuilder is free to view a demo application and design app UI. Paid plans start at $39/month for Basic plan and $49/month for Professional plan. There's also a lifetime deal available with significant savings. Agency plans are available for businesses wanting to offer app building services."}, "freeTrial": {"question": "Is there a free trial or demo available?", "answer": "Yes! FluxBuilder offers a FREE demo app creation for your website without requiring purchase or subscription. You can download, install, sign up, create a new app, customize it, and generate a demo app for free to test on your phone."}, "cancel": {"question": "Can I cancel my FluxBuilder plan at any time?", "answer": "Yes, you can cancel any plan at any time. Your apps on the App Store and Google Play will continue to work normally even after cancellation."}, "planDifferences": {"question": "What's the difference between Basic and Professional plans?", "answer": "Basic plan ($39/month) includes production-ready Android & iOS apps, Windows can build iOS, extended templates, and unlimited downloads. Professional plan ($49/month) adds VIP app features, Firebase Remote Config, ChatGPT integration, native payment gateways, in-app purchases, and advanced payment options."}, "lifetime": {"question": "What is the lifetime deal?", "answer": "The lifetime deal offers 80% savings with one-time payment, unlocking Pro features and unlimited builds on cloud. This is a limited-time offer that provides permanent access to FluxBuilder's professional features without recurring monthly fees."}, "agency": {"question": "What are Agency plans?", "answer": "Agency plans are designed for businesses wanting to start their own SaaS app builder service without writing code. These plans allow you to create unlimited app services for your clients and build a white-label app building business."}}, "privacy": {"gdpr": {"question": "Is FluxBuilder GDPR compliant?", "answer": "Yes, FluxBuilder includes GDPR compliance features across all supported platforms. The platform provides account deletion capabilities, privacy controls, and data management features to help ensure compliance with privacy regulations."}, "dataStorage": {"question": "Where is my app data stored?", "answer": "FluxBuilder uses cloud storage for configurations and app data. Your app configurations are stored securely in the cloud, allowing for instant updates without re-submitting to app stores. You maintain control over your own hosting server and data."}, "accountDeletion": {"question": "Can users delete their accounts in my app?", "answer": "Yes, FluxBuilder includes account deletion features across all supported platforms. This ensures users can delete their accounts and associated data when requested, helping with privacy compliance."}, "ageRestrictions": {"question": "Does FluxBuilder support age restrictions?", "answer": "Yes, FluxBuilder includes age restriction features across all supported platforms, allowing you to implement age-based access controls and comply with regulations regarding content access for different age groups."}, "paymentSecurity": {"question": "How secure are the payment integrations?", "answer": "FluxBuilder integrates with secure, industry-standard payment gateways including Stripe, PayPal, Apple Pay, Google Pay, and many regional providers. All payment processing follows PCI compliance standards and uses native, secure payment methods."}, "dataOwnership": {"question": "Who owns the data in my app?", "answer": "You own all the data in your app. FluxBuilder connects to your existing platforms (WooCommerce, Shopify, etc.) and your own hosting server. Your customer data, products, and content remain under your control and ownership."}}}}