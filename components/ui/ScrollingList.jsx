import React, { useRef } from "react";
import lerp from "@14islands/lerp";

const ScrollingList = ({
  children,
  className = '',
  itemClassName = '',
  containerStyle = {},
  itemStyle = {},
  enableLerp = true,
  lerpFactor = 0.08,
  dragMultiplier = 2,
  autoScroll = false,
  autoScrollSpeed = 0.5,
  pauseOnHover = true,
  ...props
}) => {
  const list = useRef();
  const x = useRef({
    start: 0,
    scroll: 0,
    delta: 0,
    startTime: 0,
    hasMoved: false
  });
  const isDragging = useRef(false);
  const lerped = useRef(0);
  const id = useRef();
  const autoScrollId = useRef();
  const isPaused = useRef(false);

  const cAF = () => {
    cancelAnimationFrame(id.current);
    id.current = null;
  };

  const rAF = () => {
    if (enableLerp) {
      lerped.current = lerp(
        lerped.current,
        x.current.scroll - x.current.delta * dragMultiplier,
        lerpFactor
      );
    } else {
      lerped.current = x.current.scroll - x.current.delta * dragMultiplier;
    }

    if (
      Math.abs(list.current.scrollLeft - lerped.current) < 1 &&
      !isDragging.current
    ) {
      cAF();
    } else {
      list.current.scrollLeft = lerped.current;
      id.current = requestAnimationFrame(rAF);
    }
  };

  const start = (e) => {
    isDragging.current = true;
    list.current.classList.add("dragging");

    x.current.start = e.pageX - list.current.offsetLeft;
    x.current.scroll = list.current.scrollLeft;

    lerped.current = list.current.scrollLeft;
    id.current && cAF();
  };

  const move = (e) => {
    if (!isDragging.current) return;
    e.preventDefault();
    x.current.delta = e.pageX - list.current.offsetLeft - x.current.start;
    // Use lerping on drag if enabled
    !id.current && rAF();
  };

  const reset = () => {
    isDragging.current = false;
    list.current.classList.remove("dragging");
  };

  // Handle click events (only if not dragged)
  const handleClick = (e) => {
    // If the mouse moved significantly or dragging occurred, prevent click
    if (x.current.hasMoved || isDragging.current) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    // Allow normal click behavior for links and buttons
    return true;
  };

  // Auto-scroll functionality
  const autoScrollFunc = () => {
    if (!autoScroll || !list.current || isDragging.current || isPaused.current) return;

    const scrollContent = list.current;
    const contentWidth = scrollContent.scrollWidth / 2; // Half because we duplicate content
    const currentScroll = scrollContent.scrollLeft;

    // If we've scrolled past the first set, reset to beginning
    if (currentScroll >= contentWidth) {
      scrollContent.scrollLeft = 0;
    } else {
      // Smooth auto-scroll
      scrollContent.scrollLeft += autoScrollSpeed;
    }

    autoScrollId.current = requestAnimationFrame(autoScrollFunc);
  };

  // Start auto-scroll when component mounts
  React.useEffect(() => {
    if (autoScroll) {
      autoScrollFunc();
    }
    return () => {
      if (autoScrollId.current) {
        cancelAnimationFrame(autoScrollId.current);
      }
    };
  }, [autoScroll]);

  // Handle hover pause
  const handleMouseEnter = () => {
    if (pauseOnHover) {
      isPaused.current = true;
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      isPaused.current = false;
    }
  };

  // Base styles for the container
  const baseContainerStyle = {
    display: 'flex',
    overflowX: 'scroll',
    overflowY: 'hidden',
    WebkitOverflowScrolling: 'touch',
    cursor: 'grab',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    ...containerStyle,
  };

  return (
    <>
      <style jsx>{`
        .drag-list {
          display: flex;
          overflow-x: scroll;
          overflow-y: hidden;
          -webkit-overflow-scrolling: touch;
          cursor: grab;
          scrollbar-width: none;
          -ms-overflow-style: none;
        }

        .drag-list::-webkit-scrollbar {
          display: none;
        }

        .drag-list.dragging {
          cursor: grabbing;
        }

        .drag-item {
          aspect-ratio: 0.86;
          flex: 0 0 250px;
          margin: 0 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>
      <div
        className={`drag-list ${className}`}
        ref={list}
        style={baseContainerStyle}
        onMouseDown={start}
        onMouseMove={move}
        onMouseUp={reset}
        onMouseLeave={() => {
          reset();
          handleMouseLeave();
        }}
        onMouseEnter={handleMouseEnter}
        onClick={handleClick}
        {...props}
      >
        {children ? (
          React.Children.map(children, (child, index) => (
            <div
              key={index}
              className={`drag-item ${itemClassName}`}
              style={itemStyle}
            >
              {child}
            </div>
          ))
        ) : (
          // Fallback demo content
          [...Array(20).keys()].map((i) => (
            <div key={i} className="drag-item" style={itemStyle}>
              {i}
            </div>
          ))
        )}
      </div>
    </>
  );
};

export default ScrollingList;
