import React, { useRef, useEffect, useCallback } from "react";
import lerp from "@14islands/lerp";

const ScrollingList = ({
  children,
  className = '',
  itemClassName = '',
  containerStyle = {},
  itemStyle = {},
  enableLerp = true,
  lerpFactor = 0.08,
  dragMultiplier = 2,
  autoScroll = false,
  autoScrollSpeed = 0.5,
  pauseOnHover = true,
  infiniteLoop = true,
  ...props
}) => {
  const list = useRef();
  const x = useRef({
    start: 0,
    scroll: 0,
    delta: 0,
    startTime: 0,
    hasMoved: false
  });
  const isDragging = useRef(false);
  const lerped = useRef(0);
  const id = useRef();
  const autoScrollId = useRef();
  const isPaused = useRef(false);
  const contentWidth = useRef(0);
  const isResetting = useRef(false);

  // Calculate content width for infinite loop
  const updateContentWidth = useCallback(() => {
    if (list.current && infiniteLoop) {
      // Calculate the width of the first set of items
      const allItems = list.current.children;
      const totalItems = allItems.length;
      const halfItems = totalItems / 2;

      let firstSetWidth = 0;
      for (let i = 0; i < halfItems; i++) {
        if (allItems[i]) {
          firstSetWidth += allItems[i].offsetWidth;
        }
      }

      contentWidth.current = firstSetWidth;
    }
  }, [infiniteLoop]);

  // Check and handle infinite loop reset
  const checkInfiniteLoop = useCallback(() => {
    if (!infiniteLoop || !list.current || isResetting.current) return;

    const scrollLeft = list.current.scrollLeft;
    const maxScroll = contentWidth.current;

    if (scrollLeft >= maxScroll) {
      isResetting.current = true;
      list.current.scrollLeft = 0;
      lerped.current = 0;
      x.current.scroll = 0;
      setTimeout(() => {
        isResetting.current = false;
      }, 16); // One frame delay
    } else if (scrollLeft < 0) {
      isResetting.current = true;
      list.current.scrollLeft = maxScroll - 1;
      lerped.current = maxScroll - 1;
      x.current.scroll = maxScroll - 1;
      setTimeout(() => {
        isResetting.current = false;
      }, 16);
    }
  }, [infiniteLoop]);

  const cAF = () => {
    cancelAnimationFrame(id.current);
    id.current = null;
  };

  const rAF = () => {
    if (enableLerp) {
      lerped.current = lerp(
        lerped.current,
        x.current.scroll - x.current.delta * dragMultiplier,
        lerpFactor
      );
    } else {
      lerped.current = x.current.scroll - x.current.delta * dragMultiplier;
    }

    if (
      Math.abs(list.current.scrollLeft - lerped.current) < 1 &&
      !isDragging.current
    ) {
      cAF();
    } else {
      list.current.scrollLeft = lerped.current;
      if (infiniteLoop) {
        checkInfiniteLoop();
      }
      id.current = requestAnimationFrame(rAF);
    }
  };

  const start = (e) => {
    isDragging.current = true;
    list.current.classList.add("dragging");

    x.current.start = e.pageX - list.current.offsetLeft;
    x.current.scroll = list.current.scrollLeft;

    lerped.current = list.current.scrollLeft;
    id.current && cAF();
  };

  const move = (e) => {
    if (!isDragging.current) return;
    e.preventDefault();
    x.current.delta = e.pageX - list.current.offsetLeft - x.current.start;
    // Use lerping on drag if enabled
    !id.current && rAF();
  };

  const reset = () => {
    isDragging.current = false;
    list.current.classList.remove("dragging");
  };

  // Handle click events (only if not dragged)
  const handleClick = (e) => {
    // If the mouse moved significantly or dragging occurred, prevent click
    if (x.current.hasMoved || isDragging.current) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    // Allow normal click behavior for links and buttons
    return true;
  };

  // Handle scroll events for infinite loop
  const handleScroll = useCallback(() => {
    if (infiniteLoop && !isDragging.current && !isResetting.current) {
      checkInfiniteLoop();
    }
  }, [infiniteLoop, checkInfiniteLoop]);

  // Auto-scroll functionality
  const autoScrollFunc = () => {
    if (!autoScroll || !list.current || isDragging.current || isPaused.current || isResetting.current) return;

    const scrollContent = list.current;
    const maxScroll = infiniteLoop ? contentWidth.current : scrollContent.scrollWidth - scrollContent.clientWidth;
    const currentScroll = scrollContent.scrollLeft;

    if (infiniteLoop) {
      // For infinite loop, just keep scrolling and let checkInfiniteLoop handle the reset
      scrollContent.scrollLeft += autoScrollSpeed;
      checkInfiniteLoop();
    } else {
      // Original behavior for non-infinite scroll
      if (currentScroll >= maxScroll) {
        scrollContent.scrollLeft = 0;
      } else {
        scrollContent.scrollLeft += autoScrollSpeed;
      }
    }

    autoScrollId.current = requestAnimationFrame(autoScrollFunc);
  };

  // Initialize content width and set up event listeners
  useEffect(() => {
    if (infiniteLoop && list.current) {
      // Initial calculation
      setTimeout(() => updateContentWidth(), 0);

      // Add scroll event listener for infinite loop
      const scrollElement = list.current;
      scrollElement.addEventListener('scroll', handleScroll, { passive: true });

      // Add resize observer to recalculate on size changes
      const resizeObserver = new ResizeObserver(() => {
        updateContentWidth();
      });

      resizeObserver.observe(scrollElement);

      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
      };
    }
  }, [infiniteLoop, updateContentWidth, handleScroll, children]);

  // Start auto-scroll when component mounts
  useEffect(() => {
    if (autoScroll) {
      autoScrollFunc();
    }
    return () => {
      if (autoScrollId.current) {
        cancelAnimationFrame(autoScrollId.current);
      }
    };
  }, [autoScroll, infiniteLoop]);

  // Handle hover pause
  const handleMouseEnter = () => {
    if (pauseOnHover) {
      isPaused.current = true;
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      isPaused.current = false;
    }
  };

  // Base styles for the container
  const baseContainerStyle = {
    display: 'flex',
    overflowX: 'scroll',
    overflowY: 'hidden',
    WebkitOverflowScrolling: 'touch',
    cursor: 'grab',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    ...containerStyle,
  };

  return (
    <>
      <style jsx>{`
        .drag-list {
          display: flex;
          overflow-x: scroll;
          overflow-y: hidden;
          -webkit-overflow-scrolling: touch;
          cursor: grab;
          scrollbar-width: none;
          -ms-overflow-style: none;
        }

        .drag-list::-webkit-scrollbar {
          display: none;
        }

        .drag-list.dragging {
          cursor: grabbing;
        }

        .drag-item {
          aspect-ratio: 0.86;
          flex: 0 0 250px;
          margin: 0 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>
      <div
        className={`drag-list ${className}`}
        ref={list}
        style={baseContainerStyle}
        onMouseDown={start}
        onMouseMove={move}
        onMouseUp={reset}
        onMouseLeave={() => {
          reset();
          handleMouseLeave();
        }}
        onMouseEnter={handleMouseEnter}
        onClick={handleClick}
        {...props}
      >
        {children ? (
          infiniteLoop ? (
            // Duplicate content for infinite loop
            <>
              {React.Children.map(children, (child, index) => (
                <div
                  key={`first-${index}`}
                  className={`drag-item ${itemClassName}`}
                  style={itemStyle}
                >
                  {child}
                </div>
              ))}
              {React.Children.map(children, (child, index) => (
                <div
                  key={`second-${index}`}
                  className={`drag-item ${itemClassName}`}
                  style={itemStyle}
                >
                  {child}
                </div>
              ))}
            </>
          ) : (
            // Normal content without duplication
            React.Children.map(children, (child, index) => (
              <div
                key={index}
                className={`drag-item ${itemClassName}`}
                style={itemStyle}
              >
                {child}
              </div>
            ))
          )
        ) : (
          // Fallback demo content
          infiniteLoop ? (
            <>
              {[...Array(20).keys()].map((i) => (
                <div key={`first-${i}`} className="drag-item" style={itemStyle}>
                  {i}
                </div>
              ))}
              {[...Array(20).keys()].map((i) => (
                <div key={`second-${i}`} className="drag-item" style={itemStyle}>
                  {i}
                </div>
              ))}
            </>
          ) : (
            [...Array(20).keys()].map((i) => (
              <div key={i} className="drag-item" style={itemStyle}>
                {i}
              </div>
            ))
          )
        )}
      </div>
    </>
  );
};

export default ScrollingList;
