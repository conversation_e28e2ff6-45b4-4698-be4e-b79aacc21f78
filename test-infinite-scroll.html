<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinite Scroll Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-item {
            width: 200px;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin: 0 10px;
            flex-shrink: 0;
        }
        
        .scroll-container {
            margin: 40px 0;
        }
        
        h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .demo-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ScrollingList Infinite Loop Test</h1>
        
        <div class="demo-section">
            <h2>🔄 Infinite Loop with Auto-scroll</h2>
            <div class="info">
                <strong>Features:</strong> Seamless infinite loop, auto-scroll, pause on hover, drag to scroll
            </div>
            <div class="controls">
                <button onclick="toggleAutoScroll()">Toggle Auto-scroll</button>
                <button onclick="changeSpeed()">Change Speed</button>
            </div>
            <div id="infinite-scroll" class="scroll-container"></div>
        </div>
        
        <div class="demo-section">
            <h2>📜 Regular Scroll (No Loop)</h2>
            <div class="info">
                <strong>Features:</strong> Regular scrolling behavior for comparison
            </div>
            <div id="regular-scroll" class="scroll-container"></div>
        </div>
    </div>

    <script type="module">
        // Mock React and ScrollingList for testing
        const React = {
            useRef: () => ({ current: null }),
            useEffect: (fn, deps) => fn(),
            useCallback: (fn) => fn,
            Children: {
                map: (children, fn) => children.map(fn)
            }
        };

        // Create test items
        function createTestItems(count = 8) {
            const items = [];
            for (let i = 1; i <= count; i++) {
                const item = document.createElement('div');
                item.className = 'test-item';
                item.textContent = `Item ${i}`;
                item.style.background = `linear-gradient(135deg, hsl(${i * 45}, 70%, 60%) 0%, hsl(${i * 45 + 30}, 70%, 50%) 100%)`;
                items.push(item);
            }
            return items;
        }

        // Simple infinite scroll implementation
        class SimpleInfiniteScroll {
            constructor(container, options = {}) {
                this.container = container;
                this.options = {
                    autoScroll: true,
                    autoScrollSpeed: 1,
                    pauseOnHover: true,
                    ...options
                };
                
                this.scrollContainer = null;
                this.isAutoScrolling = false;
                this.isPaused = false;
                this.animationId = null;
                this.contentWidth = 0;
                
                this.init();
            }
            
            init() {
                this.createScrollContainer();
                this.setupEventListeners();
                if (this.options.autoScroll) {
                    this.startAutoScroll();
                }
            }
            
            createScrollContainer() {
                this.scrollContainer = document.createElement('div');
                this.scrollContainer.style.cssText = `
                    display: flex;
                    overflow-x: auto;
                    overflow-y: hidden;
                    scroll-behavior: smooth;
                    scrollbar-width: none;
                    -ms-overflow-style: none;
                    cursor: grab;
                `;
                
                // Hide scrollbar
                const style = document.createElement('style');
                style.textContent = `
                    .scroll-container::-webkit-scrollbar { display: none; }
                    .scroll-container.dragging { cursor: grabbing; }
                `;
                document.head.appendChild(style);
                
                this.scrollContainer.className = 'scroll-container';
                this.container.appendChild(this.scrollContainer);
                
                // Add items (duplicate for infinite effect)
                const items = createTestItems();
                items.forEach(item => this.scrollContainer.appendChild(item.cloneNode(true)));
                items.forEach(item => this.scrollContainer.appendChild(item.cloneNode(true)));
                
                // Calculate content width
                setTimeout(() => {
                    this.contentWidth = this.scrollContainer.scrollWidth / 2;
                }, 0);
            }
            
            setupEventListeners() {
                if (this.options.pauseOnHover) {
                    this.scrollContainer.addEventListener('mouseenter', () => {
                        this.isPaused = true;
                    });
                    
                    this.scrollContainer.addEventListener('mouseleave', () => {
                        this.isPaused = false;
                    });
                }
                
                // Handle infinite loop
                this.scrollContainer.addEventListener('scroll', () => {
                    if (this.scrollContainer.scrollLeft >= this.contentWidth) {
                        this.scrollContainer.scrollLeft = 0;
                    }
                });
            }
            
            startAutoScroll() {
                this.isAutoScrolling = true;
                this.autoScroll();
            }
            
            stopAutoScroll() {
                this.isAutoScrolling = false;
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
            }
            
            autoScroll() {
                if (!this.isAutoScrolling || this.isPaused) {
                    this.animationId = requestAnimationFrame(() => this.autoScroll());
                    return;
                }
                
                this.scrollContainer.scrollLeft += this.options.autoScrollSpeed;
                this.animationId = requestAnimationFrame(() => this.autoScroll());
            }
            
            toggleAutoScroll() {
                if (this.isAutoScrolling) {
                    this.stopAutoScroll();
                } else {
                    this.startAutoScroll();
                }
                return this.isAutoScrolling;
            }
            
            setSpeed(speed) {
                this.options.autoScrollSpeed = speed;
            }
        }

        // Initialize demos
        const infiniteScrollDemo = new SimpleInfiniteScroll(
            document.getElementById('infinite-scroll'),
            { autoScroll: true, autoScrollSpeed: 1, pauseOnHover: true }
        );

        const regularScrollDemo = new SimpleInfiniteScroll(
            document.getElementById('regular-scroll'),
            { autoScroll: false }
        );

        // Global controls
        let currentSpeed = 1;
        const speeds = [0.5, 1, 2, 3];
        let speedIndex = 1;

        window.toggleAutoScroll = () => {
            const isRunning = infiniteScrollDemo.toggleAutoScroll();
            event.target.textContent = isRunning ? 'Stop Auto-scroll' : 'Start Auto-scroll';
        };

        window.changeSpeed = () => {
            speedIndex = (speedIndex + 1) % speeds.length;
            currentSpeed = speeds[speedIndex];
            infiniteScrollDemo.setSpeed(currentSpeed);
            event.target.textContent = `Speed: ${currentSpeed}x`;
        };
    </script>
</body>
</html>
