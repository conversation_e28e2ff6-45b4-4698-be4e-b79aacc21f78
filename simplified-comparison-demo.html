<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Comparison Table - Before vs After</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .label {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .before-label {
            background: #ef4444;
            color: white;
        }
        
        .after-label {
            background: #10b981;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Simplified Comparison Table Design</h1>
            <p class="text-xl text-gray-600">Removed shadows and simplified the design to match the home page style</p>
        </div>

        <!-- Before Design -->
        <div class="comparison-section before">
            <div class="before-label label">❌ BEFORE - Complex Design with Shadows</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Heavy Design with Multiple Shadows</h3>
            
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
                <!-- Complex Header -->
                <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8">
                    <div class="grid grid-cols-3 gap-4 items-center">
                        <div class="text-left">
                            <h3 class="text-lg font-semibold text-white">Features</h3>
                        </div>
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-white/90">Other App Builders</h3>
                        </div>
                        <div class="text-center">
                            <div class="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                                <span class="text-lg font-bold text-white">FluxBuilder</span>
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Complex Rows -->
                <div class="divide-y divide-gray-100">
                    <div class="grid grid-cols-3 gap-4 items-center px-6 py-6 hover:bg-gray-50/50">
                        <div class="flex items-start gap-3">
                            <span class="text-2xl">🔓</span>
                            <span class="text-base text-gray-800 font-medium">Include Source code to unlock all customization</span>
                        </div>
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100">
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-green-100">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Complex CTA -->
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-8 text-center">
                    <p class="text-gray-600 mb-4">Ready to experience the FluxBuilder difference?</p>
                    <div class="flex gap-3 justify-center">
                        <button class="px-6 py-3 bg-blue-600 text-white rounded-lg shadow-md">Download FluxBuilder</button>
                        <button class="px-6 py-3 bg-white border border-blue-600 text-blue-600 rounded-lg shadow-sm">View All Features</button>
                    </div>
                </div>
            </div>
            
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-red-800 mb-2">Issues with the complex design:</h4>
                <ul class="text-red-700 space-y-1">
                    <li>• Heavy use of shadows (shadow-xl, shadow-md, shadow-sm)</li>
                    <li>• Complex gradient backgrounds</li>
                    <li>• Too many visual layers and effects</li>
                    <li>• Doesn't match the clean home page style</li>
                    <li>• Overwhelming visual hierarchy</li>
                </ul>
            </div>
        </div>

        <!-- After Design -->
        <div class="comparison-section after">
            <div class="after-label label">✅ AFTER - Clean Design Without Shadows</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Simple, Clean Design Matching Home Page</h3>
            
            <div class="max-w-4xl mx-auto">
                <!-- Simple Header -->
                <div class="grid grid-cols-3 gap-4 items-center mb-8 px-4">
                    <div class="text-left">
                        <h3 class="text-lg font-semibold text-gray-900">Features</h3>
                    </div>
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-600">Other App Builders</h3>
                    </div>
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-blue-600">FluxBuilder</h3>
                    </div>
                </div>
                
                <!-- Simple Rows -->
                <div class="space-y-4">
                    <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6">
                        <div class="grid grid-cols-3 gap-4 items-center">
                            <div class="flex items-start gap-3">
                                <span class="text-xl">🔓</span>
                                <span class="text-base text-gray-800 font-medium">Include Source code to unlock all customization</span>
                            </div>
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-50">
                                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-50">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6">
                        <div class="grid grid-cols-3 gap-4 items-center">
                            <div class="flex items-start gap-3">
                                <span class="text-xl">🏢</span>
                                <span class="text-base text-gray-800 font-medium">Support Agency white-label and Dashboard</span>
                            </div>
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-50">
                                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-50">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Simple CTA -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center mt-8">
                    <p class="text-gray-600 mb-6">Ready to experience the FluxBuilder difference?</p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <button class="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold">Download FluxBuilder</button>
                        <button class="px-6 py-3 bg-white border border-blue-600 text-blue-600 rounded-lg font-semibold">View All Features</button>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-green-800 mb-2">Improvements in the simplified design:</h4>
                <ul class="text-green-700 space-y-1">
                    <li>• Removed all shadows for cleaner appearance</li>
                    <li>• Simple border-based cards instead of complex containers</li>
                    <li>• Clean typography hierarchy</li>
                    <li>• Matches the home page design language</li>
                    <li>• Better focus on content over visual effects</li>
                    <li>• Smaller, more subtle icons and indicators</li>
                </ul>
            </div>
        </div>

        <!-- Design Principles -->
        <div class="bg-white rounded-xl p-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">🎨 Design Principles Applied</h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Remove Shadows</h3>
                    <p class="text-gray-600 text-sm">Eliminated all shadow effects to create a cleaner, more modern appearance</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Simplify Layout</h3>
                    <p class="text-gray-600 text-sm">Used simple cards with borders instead of complex nested containers</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Match Home Style</h3>
                    <p class="text-gray-600 text-sm">Aligned design language with the existing home page components</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
