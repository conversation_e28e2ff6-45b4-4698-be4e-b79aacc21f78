<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Readability Improvement - Before vs After</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .label {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .before-label {
            background: #ef4444;
            color: white;
        }
        
        .after-label {
            background: #10b981;
            color: white;
        }

        /* Dark background for CTA section simulation */
        .cta-bg {
            background: #000;
            padding: 60px 20px;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .cta-bg::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .cta-bg::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, 50%);
        }

        /* Before styles - tight line height */
        .old-heading {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            line-height: 48px;
            letter-spacing: -0.025em;
            text-align: center;
            max-width: 48rem;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        /* After styles - improved line height and structure */
        .new-heading {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            line-height: 58px;
            letter-spacing: -0.025em;
            text-align: center;
            max-width: 48rem;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        .new-heading .emphasis {
            font-weight: 700;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .old-heading, .new-heading {
                font-size: 2rem;
                line-height: 2.5rem;
            }
            .new-heading {
                line-height: 3rem;
            }
        }

        /* Visual indicators */
        .line-indicator {
            position: absolute;
            left: -20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ef4444;
        }

        .line-indicator.good {
            background: #10b981;
        }

        .spacing-demo {
            position: relative;
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Text Readability Improvement</h1>
            <p class="text-xl text-gray-600">Fixing line height and text flow for better scanning and reading experience</p>
        </div>

        <!-- Problem Statement -->
        <div class="bg-white rounded-xl p-8 mb-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🚨 Issues Identified</h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="font-semibold text-yellow-800 mb-2">Text nằm sát khó đọc; Ngắt dòng chưa hợp lí:</h3>
                <ul class="text-yellow-700 space-y-1">
                    <li>• <strong>Khó scan text:</strong> Text nằm quá sát nhau, line-height quá thấp</li>
                    <li>• <strong>Ngắt dòng chưa hợp lí:</strong> "in" tách biệt với "minutes" gián đoạn flow đọc</li>
                    <li>• <strong>Thiếu emphasis:</strong> Không có visual emphasis cho phần quan trọng</li>
                </ul>
            </div>
        </div>

        <!-- Before Design -->
        <div class="comparison-section before">
            <div class="before-label label">❌ BEFORE - Poor Readability</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Tight Line Height & Poor Text Flow</h3>
            
            <div class="cta-bg">
                <h2 class="old-heading">
                    Launch Your Mobile App in Minutes, Not Months
                </h2>
            </div>
            
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-red-800 mb-2">Problems with the original text:</h4>
                <ul class="text-red-700 space-y-1">
                    <li>• <strong>Line height too tight:</strong> 48px makes text cramped and hard to scan</li>
                    <li>• <strong>Poor line breaks:</strong> "in" separated from "minutes" disrupts reading flow</li>
                    <li>• <strong>No visual emphasis:</strong> Key message "in minutes" doesn't stand out</li>
                    <li>• <strong>Difficult to scan:</strong> Users can't quickly grasp the main benefit</li>
                    <li>• <strong>Inconsistent hierarchy:</strong> All text has same visual weight</li>
                </ul>
            </div>
        </div>

        <!-- After Design -->
        <div class="comparison-section after">
            <div class="after-label label">✅ AFTER - Improved Readability</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Better Line Height & Logical Text Flow</h3>
            
            <div class="cta-bg">
                <h2 class="new-heading">
                    <span style="display: block;">Launch your mobile app</span>
                    <span style="display: block;">
                        <span class="emphasis">in minutes</span>, not months
                    </span>
                </h2>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-green-800 mb-2">Improvements made:</h4>
                <ul class="text-green-700 space-y-1">
                    <li>• <strong>Increased line height:</strong> 48px → 58px for better breathing room</li>
                    <li>• <strong>Logical line breaks:</strong> "Launch your mobile app" (line 1) + "in minutes, not months" (line 2)</li>
                    <li>• <strong>Visual emphasis:</strong> "in minutes" is bold to highlight key benefit</li>
                    <li>• <strong>Better scanning:</strong> Users can quickly identify the main value proposition</li>
                    <li>• <strong>Improved flow:</strong> Natural reading progression without interruption</li>
                </ul>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="bg-white rounded-xl p-8 border border-gray-200 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 Technical Implementation</h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Before Code:</h3>
                    <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                        <pre class="text-sm text-red-800"><code>&lt;motion.h2
  className="...leading-tight..."
  variants={itemVariants}
&gt;
  {t('home.cta.mainTitle')}
&lt;/motion.h2&gt;</code></pre>
                        <p class="text-sm text-red-600 mt-2">• Used CSS class "leading-tight" (line-height: 1.25)</p>
                        <p class="text-sm text-red-600">• Single text block with no structure</p>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">After Code:</h3>
                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                        <pre class="text-sm text-green-800"><code>&lt;motion.h2
  style={{ lineHeight: '58px' }}
  variants={itemVariants}
&gt;
  &lt;span className="block"&gt;Launch your mobile app&lt;/span&gt;
  &lt;span className="block"&gt;
    &lt;span className="font-bold"&gt;in minutes&lt;/span&gt;, not months
  &lt;/span&gt;
&lt;/motion.h2&gt;</code></pre>
                        <p class="text-sm text-green-600 mt-2">• Fixed line-height: 58px for better spacing</p>
                        <p class="text-sm text-green-600">• Structured with logical line breaks</p>
                        <p class="text-sm text-green-600">• Bold emphasis on key benefit</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Typography Guidelines -->
        <div class="bg-white rounded-xl p-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📐 Typography Best Practices Applied</h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h7"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Line Height</h3>
                    <p class="text-gray-600 text-sm">Increased from 48px to 58px for better readability and visual breathing room</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Text Structure</h3>
                    <p class="text-gray-600 text-sm">Logical line breaks that follow natural reading patterns and meaning groups</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Visual Emphasis</h3>
                    <p class="text-gray-600 text-sm">Bold weight on "in minutes" to highlight the key value proposition and speed benefit</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
