<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Consistency Fix - Before vs After</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .label {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .before-label {
            background: #ef4444;
            color: white;
        }
        
        .after-label {
            background: #10b981;
            color: white;
        }

        /* Simulate the old inconsistent styles */
        .old-btn-1 {
            padding: 8px 16px;
            font-weight: 400;
            border-radius: 6px;
            background: #3b82f6;
            color: white;
            transition: background-color 0.2s;
        }
        .old-btn-1:hover {
            background: #1d4ed8;
        }

        .old-btn-2 {
            padding: 15px 20px;
            font-weight: 600;
            border-radius: 9px;
            background: #3b82f6;
            color: white;
            transition: background-color 0.3s;
        }
        .old-btn-2:hover {
            background: #2cc1ff;
        }

        .old-btn-3 {
            padding: 12px 24px;
            font-weight: 500;
            border-radius: 8px;
            background: #3b82f6;
            color: white;
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
        }
        .old-btn-3:hover {
            background: #1e40af;
            transform: scale(1.02);
        }
        .old-btn-3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff5e00, #ff0062, #8000ff, #0d6aff, #00d1ff);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .old-btn-3:hover::after {
            opacity: 1;
        }

        /* New unified styles */
        .new-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 16px;
            border-radius: 8px;
            background: #3b82f6;
            color: white;
            transition: all 0.3s;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
        }
        .new-btn:hover {
            background: rgba(59, 130, 246, 0.9);
            transform: scale(1.02);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .new-btn:active {
            transform: scale(0.98);
        }
        .new-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
        }

        .new-btn-sm {
            padding: 8px 16px;
            font-size: 14px;
        }

        .new-btn-lg {
            padding: 16px 32px;
            font-size: 18px;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">CTA Button Consistency Fix</h1>
            <p class="text-xl text-gray-600">Unifying font weights, hover states, and border radius across all CTA buttons</p>
        </div>

        <!-- Problem Statement -->
        <div class="bg-white rounded-xl p-8 mb-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🚨 Issues Identified</h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 class="font-semibold text-yellow-800 mb-2">CTA Button chưa thống nhất:</h3>
                <ul class="text-yellow-700 space-y-1">
                    <li>• <strong>Khác font:</strong> "View all apps" font regular, "Get free download" font semibold</li>
                    <li>• <strong>Hover state khác nhau:</strong> "Get free download" chuyển màu xanh nhạt, "View all apps" chuyển màu xanh đậm, "Get started for free" có gradient highlight ở dưới</li>
                    <li>• <strong>Bo góc không đều:</strong> Các button có border-radius khác nhau</li>
                </ul>
            </div>
        </div>

        <!-- Before Design -->
        <div class="comparison-section before">
            <div class="before-label label">❌ BEFORE - Inconsistent Buttons</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Different Styles Across the Site</h3>
            
            <div class="bg-white rounded-lg p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">"View all apps" button:</h4>
                        <button class="old-btn-1">View all apps</button>
                        <p class="text-sm text-gray-500 mt-2">• Font: regular (400) • Padding: 8px 16px • Border-radius: 6px • Hover: dark blue</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">"Get free download" button:</h4>
                        <button class="old-btn-2">Get free download</button>
                        <p class="text-sm text-gray-500 mt-2">• Font: semibold (600) • Padding: 15px 20px • Border-radius: 9px • Hover: light blue (#2cc1ff)</p>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">"Get started for free" button:</h4>
                        <button class="old-btn-3">Get started for free</button>
                        <p class="text-sm text-gray-500 mt-2">• Font: medium (500) • Padding: 12px 24px • Border-radius: 8px • Hover: scale + rainbow gradient bottom</p>
                    </div>
                </div>
                
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                    <h4 class="font-semibold text-red-800 mb-2">Problems with inconsistent buttons:</h4>
                    <ul class="text-red-700 space-y-1">
                        <li>• Users get confused by different visual treatments</li>
                        <li>• Inconsistent brand experience</li>
                        <li>• Different importance levels implied</li>
                        <li>• Harder to maintain and update</li>
                        <li>• Poor accessibility due to varying focus states</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- After Design -->
        <div class="comparison-section after">
            <div class="after-label label">✅ AFTER - Unified Button System</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Consistent Design Language</h3>
            
            <div class="bg-white rounded-lg p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">All CTA buttons now use unified styling:</h4>
                        <div class="flex flex-wrap gap-4">
                            <button class="new-btn new-btn-sm">View all apps</button>
                            <button class="new-btn">Get free download</button>
                            <button class="new-btn new-btn-lg">Get started for free</button>
                        </div>
                    </div>
                    
                    <div class="grid md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-gray-50 p-3 rounded">
                            <h5 class="font-semibold mb-2">Typography</h5>
                            <p>• Font: semibold (600) consistently</p>
                            <p>• Size: sm/base/lg variants</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded">
                            <h5 class="font-semibold mb-2">Spacing</h5>
                            <p>• Small: 8px 16px</p>
                            <p>• Medium: 12px 24px</p>
                            <p>• Large: 16px 32px</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded">
                            <h5 class="font-semibold mb-2">Interactions</h5>
                            <p>• Border-radius: 8px</p>
                            <p>• Hover: scale(1.02) + shadow</p>
                            <p>• Active: scale(0.98)</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                    <h4 class="font-semibold text-green-800 mb-2">Benefits of unified system:</h4>
                    <ul class="text-green-700 space-y-1">
                        <li>• Consistent user experience across all pages</li>
                        <li>• Clear visual hierarchy with size variants</li>
                        <li>• Unified hover and focus states</li>
                        <li>• Easier maintenance and updates</li>
                        <li>• Better accessibility with consistent focus rings</li>
                        <li>• Professional, cohesive brand appearance</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Implementation Details -->
        <div class="bg-white rounded-xl p-8 border border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 Implementation Details</h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">SCSS Changes Made:</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <pre class="text-sm"><code>.btn {
  @apply font-semibold transition-all duration-300 rounded-lg;
  @apply hover:scale-[1.02] active:scale-[0.98];
  @apply shadow-md hover:shadow-lg;
  
  &-sm { @apply px-4 py-2 text-sm; }
  &-md { @apply px-6 py-3 text-base; }
  &-lg { @apply px-8 py-4 text-lg; }
  
  &-cta {
    @apply text-white bg-primary hover:bg-primary/90;
  }
}</code></pre>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Files Updated:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>✅ <code>styles/buttons.scss</code> - Unified button system</li>
                        <li>✅ <code>styles/navigation.scss</code> - Updated download-btn</li>
                        <li>✅ <code>layouts/partials/Header.js</code> - Consistent CTA buttons</li>
                        <li>✅ <code>layouts/fluxbuilder/MiniFeaturedApps.js</code> - View all apps button</li>
                        <li>✅ <code>layouts/fluxbuilder/HowItWorksSection.js</code> - Start now button</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
