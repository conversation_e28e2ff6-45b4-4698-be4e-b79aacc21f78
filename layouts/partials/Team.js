import React from 'react';
import Image from 'next/image';
import { useTranslation } from "@/lib/utils/i18n";

const Team = () => {
  const { t } = useTranslation();
  
  return (
    <div className="pt-16 pb-10 overflow-hidden bg-white">
      <div className="px-6 mx-auto max-w-7xl lg:px-8">
        <div
          className="grid max-w-2xl grid-cols-1 mx-auto gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">

          <div className="max-w-2xl mx-auto lg:mr-0 lg:max-w-lg">
            <Image 
              className="object-cover w-[80px] mx-auto" 
              src="/images/users/power.png" 
              alt={t('team.power_elite_badge', 'Power Elite Author Badge')} 
              width={80}
              height={80}
            />

            <h2 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {t('team.trusted_heading', 'Trusted by thousands of businesses worldwide')}
            </h2>

            <p className="mt-6 text-[1.2em] leading-8 text-gray-900">
              {t('team.envato_description', 'InspireUI has sold more than $1 million dollars worth of items on the Envato Marketplaces and is the Power Elite Author')}

              <a className='mt-2 text-right cursor-pointer text-[0.95em] text-blue-700 leading-6 block' href="https://codecanyon.net/user/inspireui">
                {t('team.view_detail', 'View Detail')}
              </a>
            </p>
            
            <dl className="grid max-w-xl grid-cols-1 gap-8 mt-8 sm:grid-cols-2">
              <a target='_blank' href="https://codecanyon.net/user/inspireui" rel="noopener noreferrer">
                <div className="flex flex-col pl-6 border-l gap-y-3 border-gray-900/10">
                  <dt className="text-sm leading-6 text-gray-600">
                    {t('team.happy_customers', 'Happy Customers')}
                  </dt>
                  <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900">
                    50,000+
                  </dd>
                </div>
              </a>

              <div className="flex flex-col pl-6 border-l gap-y-3 border-gray-900/10">
                <dt className="leading-6 text-gray-600">{t('team.countries', 'Countries represented')}</dt>
                <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900">140</dd>
              </div>

              <a target='_blank' href="https://codecanyon.net/user/inspireui" rel="noopener noreferrer">
                <div className="flex flex-col pl-6 border-l gap-y-3 border-gray-900/10">
                  <dt className="leading-6 text-gray-600">{t('team.reviews', '5-Star Reviews')}</dt>
                  <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900">1000+ ⭐️</dd>
                </div>
              </a>

              <div className="flex flex-col pl-6 border-l gap-y-3 border-gray-900/10">
                <dt className="leading-6 text-gray-600">{t('team.success_apps', 'Success Apps on Stores')}</dt>
                <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900">19,000+</dd>
              </div>
            </dl>
          </div>

          <div className="flex items-start justify-end lg:order-first">
            <Image 
              alt={t('team.metrics_image', 'InspireUI business metrics and success statistics')}
              loading="lazy"
              width={600} 
              height={400}
              className="w-full max-w-none rounded"
              src="/images/numbers.jpg" 
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Team;
