import useOs from "../../hooks/useOs";
import {useSearchContext} from "../../context/searchContext";
import {useContrastContext} from "../../context/contrastContext";
import {useEffect, useState, useRef} from "react";
import MegaMenu from "../components/MegaMenu";
import ShowcaseMegaMenu from "../components/ShowcaseMegaMenu";
import LanguageSwitcher from "../components/LanguageSwitcher";
import SearchButton from "../components/SearchButton";
import { useTranslation } from "../../lib/utils/i18n";
import LocalizedLink from "../components/LocalizedLink";
import Search from "../components/search/Search";

const Header = () => {
    const macOs = useOs();
    const {searchModal, setSearchModal} = useSearchContext();
    const {highContrast, toggleHighContrast} = useContrastContext();
    const { t } = useTranslation();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const mobileMenuRef = useRef(null);
    const [isVisible, setIsVisible] = useState(true);
    const [lastScrollY, setLastScrollY] = useState(0);

    useEffect(() => {
        const controlNavbar = () => {
            if (typeof window !== 'undefined') {
                const currentScrollY = window.scrollY;

                // Show header when scrolling up or at top
                if (currentScrollY < lastScrollY || currentScrollY < 50) {
                    setIsVisible(true);
                }
                // Hide header when scrolling down
                else if (currentScrollY > lastScrollY) {
                    setIsVisible(false);
                }

                setLastScrollY(currentScrollY);
            }
        };

        if (typeof window !== 'undefined') {
            window.addEventListener('scroll', controlNavbar);

            // Cleanup
            return () => {
                window.removeEventListener('scroll', controlNavbar);
            };
        }
    }, [lastScrollY]);

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") {
                setSearchModal(false);
                setMobileMenuOpen(false);
            } else if (macOs && e.metaKey && e.key === "k") {
                e.preventDefault();
                setSearchModal(!searchModal);
            } else if (e.ctrlKey && e.key === "k") {
                e.preventDefault();
                setSearchModal(!searchModal);
            } else if ((macOs && e.metaKey && e.key === "h") || (e.ctrlKey && e.key === "h")) {
                // Toggle high contrast mode with Cmd+H or Ctrl+H
                e.preventDefault();
                toggleHighContrast();
            }
        };

        document.addEventListener("keydown", handleKeyDown);

        // Close mobile menu when clicking outside
        const handleClickOutside = (event) => {
            if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
                setMobileMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener("keydown", handleKeyDown);
            document.removeEventListener('mousedown', handleClickOutside);
        };

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [searchModal, macOs]);

    return (
        <>
            {/* Skip to content link for keyboard users */}
            <a href="#main-content" className="sr-only skip-to-content focus:not-sr-only focus:absolute focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black">
                Skip to content
            </a>
            <header className={`fixed top-0 px-0 left-0 right-0 z-50 w-full bg-white header dark:bg-gray-900 transition-transform duration-300 ${isVisible ? 'translate-y-0' : '-translate-y-full'}`}>
                <nav className="flex flex-wrap items-center  px-4 py-2 mx-auto navbar sm:px-6 lg:px-8">

                    <div className="flex items-center">
                      <div className="flex items-center w-[140px] sm:w-[160px]">
                          <a href="/" aria-current="page"
                            className="brand w-nav-brand w--current"
                            aria-label="home"><img
                              src="/images/logo.png"
                              loading="eager" width="Auto" alt="FluxBuilder Logo" className="w-full h-auto logo"/>
                          </a>
                      </div>

                      <div className="items-center pt-2 pl-6 order-3 hidden ml-auto md:inline-block md:order-3 lg:order-2 lg:ml-0">
                        {/* Desktop Menu */}
                        <div className="items-center hidden space-x-4 lg:flex xl:space-x-6">
                            <ShowcaseMegaMenu />
                            <MegaMenu menuType="features" />
                            <MegaMenu menuType="resources" />
                            <LocalizedLink href="/agency" className="flex items-center px-1 py-2 pt-3 text-[0.95em] font-medium text-gray-700 dark:text-gray-300 transition-colors duration-300 border-b-2 border-transparent hover:text-primary dark:hover:text-darkmode-primary">
                              {t('header.agency')}
                            </LocalizedLink>
                            <LocalizedLink href="/pricing" className="flex items-center px-1 py-2 pt-3 text-[0.95em] font-medium text-gray-700 dark:text-gray-300 transition-colors duration-300 border-b-2 border-transparent hover:text-primary dark:hover:text-darkmode-primary">
                              {t('header.pricing')}
                            </LocalizedLink>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center order-2 ml-auto md:order-2 lg:order-3 lg:ml-0">
                        <div className="ml-2 md:ml-4">
                          <LanguageSwitcher variant="icons" />
                        </div>

                        <div className="items-center hidden ml-4 md:flex">
                          <div className="mr-2">
                            <SearchButton />
                          </div>

                          <LocalizedLink href="/download"
                             className="btn btn-sm download-btn">
                            {t('common.getFreeDownload', 'Get free download')}
                          </LocalizedLink>
                        </div>

                         {/* Mobile Menu Toggle Button */}
                         <button
                          className="lg:hidden ml-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative z-[101] flex items-center justify-center"
                          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                          aria-label="Toggle mobile menu"
                        >
                          <svg
                            className="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            {mobileMenuOpen ? (
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M6 18L18 6M6 6l12 12"
                              />
                            ) : (
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M4 6h16M4 12h16M4 18h16"
                              />
                            )}
                          </svg>
                        </button>
                    </div>
                </nav>
            </header>

            {/* Mobile Menu Overlay */}
            {mobileMenuOpen && (
              <div
                className="fixed inset-0 z-40 transition-opacity duration-300 ease-in-out bg-black bg-opacity-50"
                onClick={() => setMobileMenuOpen(false)}
              ></div>
            )}

            {/* Mobile Menu */}
            <div
              ref={mobileMenuRef}
              className={`fixed top-0 right-0 bottom-0 w-full sm:w-80 bg-white dark:bg-gray-900 z-50 shadow-xl overflow-y-auto transform transition-transform duration-300 ease-in-out h-screen ${mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}

            >
              <div className="flex flex-col w-full h-full overflow-y-auto">
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <a href="/" className="block">
                    <img
                      src="/images/logo.png"
                      loading="eager" width="Auto" alt="FluxBuilder Logo" className="h-8"/>
                  </a>
                  <button
                    className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative z-[101]"
                    onClick={() => setMobileMenuOpen(false)}
                    aria-label="Close mobile menu"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="flex-1 p-4">
                  {/* Mobile Mega Menu */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <ShowcaseMegaMenu isMobile={true} onMobileItemClick={() => setMobileMenuOpen(false)} />
                      <MegaMenu isMobile={true} menuType="features" onMobileItemClick={() => setMobileMenuOpen(false)} />
                      <MegaMenu isMobile={true} menuType="resources" onMobileItemClick={() => setMobileMenuOpen(false)} />
                      <LocalizedLink
                        href="/agency"
                        className="flex items-center w-full px-3 py-2 text-[0.95em] font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {t('header.agency')}
                      </LocalizedLink>
                      <LocalizedLink
                        href="/pricing"
                        className="flex items-center w-full px-3 py-2 text-[0.95em] font-medium text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {t('header.pricing')}
                      </LocalizedLink>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('common.improveReadability', 'Improved Readability')}</span>
                    <button
                      onClick={toggleHighContrast}
                      className="relative inline-flex items-center h-6 transition-colors rounded-full w-11 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      role="switch"
                      aria-checked={highContrast}
                    >
                      <span
                        className={`${highContrast ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700'} inline-block w-11 h-6 rounded-full transition-colors`}
                      />
                      <span
                        className={`${highContrast ? 'translate-x-6' : 'translate-x-1'} inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                      />
                    </button>
                  </div>

                  <div className="mb-4">
                    <span className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">{t('common.changeLanguage', 'Change the language')}</span>
                    <LanguageSwitcher variant="buttons" />
                  </div>

                  <div className="mb-4">
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false);
                        setSearchModal(true);
                      }}
                      className="flex items-center w-full px-4 py-2 mb-2 text-sm font-medium text-gray-700 transition-colors bg-gray-100 rounded-md dark:bg-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                    >
                      <svg
                        className="w-5 h-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        ></path>
                      </svg>
                      {t('common.search', 'Search')}
                    </button>
                  </div>

                  <LocalizedLink
                    href="/download"
                    className="block w-full py-3 font-medium text-center text-white transition-colors rounded-lg bg-primary hover:bg-primary/90"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {t('common.getFreeDownload', 'Get free download')}
                  </LocalizedLink>
                </div>
              </div>
            </div>

            {/* Search Modal */}
            <Search setSearchModal={setSearchModal} searchModal={searchModal} />
        </>
    );
};

export default Header;
