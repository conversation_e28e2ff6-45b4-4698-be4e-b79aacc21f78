import React from 'react';
import { useTranslation } from '@/lib/utils/i18n';

const WhyFluxBuilderSection = () => {
  const { t } = useTranslation();

  const comparisonFeatures = [
    {
      feature: t('home.whyFluxBuilder.features.sourceCode', 'Include Source code to unlock all customization'),
      others: false,
      fluxBuilder: true,
      icon: '🔓'
    },
    {
      feature: t('home.whyFluxBuilder.features.whiteLabel', 'Support Agency white-label and Dashboard'),
      others: false,
      fluxBuilder: true,
      icon: '🏢'
    },
    {
      feature: t('home.whyFluxBuilder.features.unlimitedBuild', 'Unlimited Build On Cloud'),
      others: false,
      fluxBuilder: true,
      icon: '☁️'
    },
    {
      feature: t('home.whyFluxBuilder.features.longTermSupport', 'Long term support and maintain'),
      others: false,
      fluxBuilder: true,
      icon: '🛠️'
    },
    {
      feature: t('home.whyFluxBuilder.features.productionReady', 'Build completely production app ready, not just the UI'),
      others: false,
      fluxBuilder: true,
      icon: '🚀'
    },
    {
      feature: t('home.whyFluxBuilder.features.nativePerformance', 'True native performance with Flutter framework'),
      others: false,
      fluxBuilder: true,
      icon: '⚡'
    }
  ];

  return (
    <section className="relative py-16 sm:py-20 lg:py-24 bg-gradient-to-b from-white to-gray-50">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container relative px-4 mx-auto sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="max-w-3xl mx-auto text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('home.whyFluxBuilder.title', 'Why use FluxBuilder?')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 leading-relaxed">
            {t('home.whyFluxBuilder.subtitle', 'See how FluxBuilder stands out from other app builders with professional features and unlimited possibilities.')}
          </p>
        </div>

        {/* Comparison Table */}
        <div className="max-w-4xl mx-auto">
          {/* Table Header */}
          <div className="grid grid-cols-3 gap-4 items-center mb-8 px-4">
            <div className="text-left">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                {t('home.whyFluxBuilder.tableHeader.features', 'Features')}
              </h3>
            </div>
            <div className="text-center">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-600">
                {t('home.whyFluxBuilder.tableHeader.others', 'Other App Builders')}
              </h3>
            </div>
            <div className="text-center">
              <h3 className="text-lg sm:text-xl font-semibold text-blue-600">
                FluxBuilder
              </h3>
            </div>
          </div>

          {/* Comparison Rows */}
          <div className="space-y-4">
            {comparisonFeatures.map((item, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6"
              >
                <div className="grid grid-cols-3 gap-4 items-center">
                  {/* Feature Description */}
                  <div className="flex items-start gap-3">
                    <span className="text-xl flex-shrink-0 mt-1">{item.icon}</span>
                    <span className="text-sm sm:text-base text-gray-800 font-medium leading-relaxed">
                      {item.feature}
                    </span>
                  </div>

                  {/* Other App Builders */}
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-50">
                      <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  </div>

                  {/* FluxBuilder */}
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-50">
                      <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 sm:p-8 text-center mt-8">
            <p className="text-gray-600 mb-6 text-sm sm:text-base">
              {t('home.whyFluxBuilder.cta.text', 'Ready to experience the FluxBuilder difference?')}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
              <a
                href="/download"
                className="btn btn-cta"
              >
                {t('home.whyFluxBuilder.cta.download', 'Download FluxBuilder')}
              </a>
              <a
                href="/features"
                className="btn btn-cta-outline"
              >
                {t('home.whyFluxBuilder.cta.features', 'View All Features')}
              </a>
            </div>
          </div>
        </div>

        {/* Additional Benefits */}
        <div className="max-w-4xl mx-auto mt-12 sm:mt-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
              <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('home.whyFluxBuilder.benefits.fast.title', 'Lightning Fast')}
              </h3>
              <p className="text-gray-600 text-sm">
                {t('home.whyFluxBuilder.benefits.fast.description', 'Build and deploy your app in minutes, not months')}
              </p>
            </div>

            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
              <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('home.whyFluxBuilder.benefits.professional.title', 'Professional Grade')}
              </h3>
              <p className="text-gray-600 text-sm">
                {t('home.whyFluxBuilder.benefits.professional.description', 'Enterprise-level features and security standards')}
              </p>
            </div>

            <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
              <div className="w-12 h-12 bg-purple-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('home.whyFluxBuilder.benefits.support.title', 'Dedicated Support')}
              </h3>
              <p className="text-gray-600 text-sm">
                {t('home.whyFluxBuilder.benefits.support.description', '24/7 expert support and comprehensive documentation')}
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </section>
  );
};

export default WhyFluxBuilderSection;
