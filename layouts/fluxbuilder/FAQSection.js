import { useState } from 'react';
import Link from 'next/link';
import { useTranslation } from '../../lib/utils/i18n';

const FAQItem = ({ question, answer, isOpen, toggleOpen, hideBorder, index }) => {
  return (
    <div className={`w-full ${hideBorder ? '' : 'border-b border-gray-100'}`}>
      <button
        className="flex items-center justify-between w-full px-6 py-5 text-left transition-colors duration-200 hover:bg-gray-50/50 focus:outline-none focus:bg-gray-50/50 group"
        onClick={() => toggleOpen(index)}
        aria-expanded={isOpen}
      >
        <h3 className="text-base md:text-lg font-medium text-gray-900 pr-4 group-hover:text-blue-700 transition-colors duration-200">{question}</h3>
        <div className={`flex-shrink-0 flex items-center justify-center w-8 h-8 transition-all duration-200 ${isOpen ? 'rotate-180' : ''}`}>
          <svg
            className={`w-5 h-5 transition-colors duration-200 ${isOpen ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-600'}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>
      <div className={`grid transition-all duration-300 ease-in-out ${isOpen ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'}`}>
        <div className="overflow-hidden">
          <div className="px-6 pb-5 text-base text-gray-600 prose prose-blue max-w-none leading-relaxed">
            {answer}
          </div>
        </div>
      </div>
    </div>
  );
};

const FAQSection = ({ customFaqs }) => {
  const [openIndex, setOpenIndex] = useState(0);
  const [activeCategory, setActiveCategory] = useState('general');
  const { t } = useTranslation();

  const toggleOpen = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // Comprehensive FAQ categories with real FluxBuilder information
  const faqCategories = {
    general: {
      title: t('faq.categories.general', 'General'),
      faqs: [
        {
          question: t('faq.general.whatIsFluxBuilder.question', 'What is FluxBuilder?'),
          answer: t('faq.general.whatIsFluxBuilder.answer', 'FluxBuilder is a drag-and-drop app builder that enables users to create mobile applications for iOS and Android devices without coding skills. It provides a user-friendly tool that helps businesses build Flutter mobile apps with powerful integrations, flexible design customization, and instant delivery capabilities.')
        },
        {
          question: t('faq.general.platforms.question', 'What platforms does FluxBuilder support?'),
          answer: t('faq.general.platforms.answer', 'FluxBuilder is compatible with many famous platforms including WooCommerce, WCFM, Dokan, Shopify, OpenCart, WordPress, Listeo, MyListing, ListingPro, Magento, PrestaShop, BigCommerce, Notion, and Strapi. It runs on Windows, macOS, and has a web version.')
        },
        {
          question: t('faq.general.appTypes.question', 'What kind of apps can I create with FluxBuilder?'),
          answer: t('faq.general.appTypes.answer', 'With FluxBuilder, you can create a wide range of mobile apps including e-commerce, listing directory, news, pharmacy, grocery, marketplace, food delivery, restaurant, salon, social media, fitness apps, and many more. The platform supports multipurpose app development.')
        },
        {
          question: t('faq.general.easyToUse.question', 'Is FluxBuilder easy to use for non-technical users?'),
          answer: t('faq.general.easyToUse.answer', 'Yes, FluxBuilder is specifically designed to be user-friendly with a drag-and-drop interface and intuitive controls. Non-technical users can auto-build both iOS and Android apps without any coding knowledge. The platform provides comprehensive documentation and guided walkthroughs.')
        },
        {
          question: t('faq.general.features.question', 'What are FluxBuilder\'s main features?'),
          answer: t('faq.general.features.answer', 'FluxBuilder offers powerful integrations with top eCommerce platforms, flexible design & customization with 100+ templates, instant delivery through cloud storage, cross-platform compatibility, drag-and-drop design, multi-language support (50+ languages), Firebase integration, payment gateways, and much more.')
        },
        {
          question: t('faq.general.support.question', 'What kind of support does FluxBuilder offer?'),
          answer: t('faq.general.support.answer', 'FluxBuilder offers comprehensive support including a knowledge base, community forum, support center, email support, and detailed documentation. Users can also schedule one-on-one calls with support representatives when needed.')
        }
      ]
    },
    pricing: {
      title: t('faq.categories.pricing', 'Plans & Pricing'),
      faqs: [
        {
          question: t('faq.pricing.cost.question', 'How much does FluxBuilder cost?'),
          answer: t('faq.pricing.cost.answer', 'FluxBuilder is free to view a demo application and design app UI. Paid plans start at $39/month for Basic plan and $49/month for Professional plan. There\'s also a lifetime deal available with significant savings. Agency plans are available for businesses wanting to offer app building services.')
        },
        {
          question: t('faq.pricing.freeTrial.question', 'Is there a free trial or demo available?'),
          answer: t('faq.pricing.freeTrial.answer', 'Yes! FluxBuilder offers a FREE demo app creation for your website without requiring purchase or subscription. You can download, install, sign up, create a new app, customize it, and generate a demo app for free to test on your phone.')
        },
        {
          question: t('faq.pricing.cancel.question', 'Can I cancel my FluxBuilder plan at any time?'),
          answer: t('faq.pricing.cancel.answer', 'Yes, you can cancel any plan at any time. Your apps on the App Store and Google Play will continue to work normally even after cancellation.')
        },
        {
          question: t('faq.pricing.planDifferences.question', 'What\'s the difference between Basic and Professional plans?'),
          answer: t('faq.pricing.planDifferences.answer', 'Basic plan ($39/month) includes production-ready Android & iOS apps, Windows can build iOS, extended templates, and unlimited downloads. Professional plan ($49/month) adds VIP app features, Firebase Remote Config, ChatGPT integration, native payment gateways, in-app purchases, and advanced payment options.')
        },
        {
          question: t('faq.pricing.lifetime.question', 'What is the lifetime deal?'),
          answer: t('faq.pricing.lifetime.answer', 'The lifetime deal offers 80% savings with one-time payment, unlocking Pro features and unlimited builds on cloud. This is a limited-time offer that provides permanent access to FluxBuilder\'s professional features without recurring monthly fees.')
        },
        {
          question: t('faq.pricing.agency.question', 'What are Agency plans?'),
          answer: t('faq.pricing.agency.answer', 'Agency plans are designed for businesses wanting to start their own SaaS app builder service without writing code. These plans allow you to create unlimited app services for your clients and build a white-label app building business.')
        }
      ]
    },
    privacy: {
      title: t('faq.categories.privacy', 'Privacy & Security'),
      faqs: [
        {
          question: t('faq.privacy.gdpr.question', 'Is FluxBuilder GDPR compliant?'),
          answer: t('faq.privacy.gdpr.answer', 'Yes, FluxBuilder includes GDPR compliance features across all supported platforms. The platform provides account deletion capabilities, privacy controls, and data management features to help ensure compliance with privacy regulations.')
        },
        {
          question: t('faq.privacy.dataStorage.question', 'Where is my app data stored?'),
          answer: t('faq.privacy.dataStorage.answer', 'FluxBuilder uses cloud storage for configurations and app data. Your app configurations are stored securely in the cloud, allowing for instant updates without re-submitting to app stores. You maintain control over your own hosting server and data.')
        },
        {
          question: t('faq.privacy.accountDeletion.question', 'Can users delete their accounts in my app?'),
          answer: t('faq.privacy.accountDeletion.answer', 'Yes, FluxBuilder includes account deletion features across all supported platforms. This ensures users can delete their accounts and associated data when requested, helping with privacy compliance.')
        },
        {
          question: t('faq.privacy.ageRestrictions.question', 'Does FluxBuilder support age restrictions?'),
          answer: t('faq.privacy.ageRestrictions.answer', 'Yes, FluxBuilder includes age restriction features across all supported platforms, allowing you to implement age-based access controls and comply with regulations regarding content access for different age groups.')
        },
        {
          question: t('faq.privacy.paymentSecurity.question', 'How secure are the payment integrations?'),
          answer: t('faq.privacy.paymentSecurity.answer', 'FluxBuilder integrates with secure, industry-standard payment gateways including Stripe, PayPal, Apple Pay, Google Pay, and many regional providers. All payment processing follows PCI compliance standards and uses native, secure payment methods.')
        },
        {
          question: t('faq.privacy.dataOwnership.question', 'Who owns the data in my app?'),
          answer: t('faq.privacy.dataOwnership.answer', 'You own all the data in your app. FluxBuilder connects to your existing platforms (WooCommerce, Shopify, etc.) and your own hosting server. Your customer data, products, and content remain under your control and ownership.')
        }
      ]
    }
  };

  // Use custom FAQs if provided, otherwise use the categorized FAQs
  const currentFaqs = customFaqs || faqCategories[activeCategory].faqs;

  const categories = [
    { id: 'general', name: faqCategories.general.title },
    { id: 'pricing', name: faqCategories.pricing.title },
    { id: 'privacy', name: faqCategories.privacy.title }
  ];

  return (
    <section className="relative py-24 md:py-28 lg:py-32 bg-gradient-to-b from-gray-100 to-gray-200">
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute w-[800px] h-[800px] -top-[400px] -right-[400px] bg-gradient-to-br from-blue-50/40 to-indigo-50/40 rounded-full blur-3xl"></div>
        <div className="absolute w-[600px] h-[600px] -bottom-[300px] -left-[300px] bg-gradient-to-tr from-blue-50/40 to-indigo-50/40 rounded-full blur-2xl"></div>
      </div>

      <div className="container relative px-4 mx-auto">
        <div className="max-w-3xl mx-auto mb-16 text-center">
          <span className="inline-block px-3 py-1 mb-4 text-xs font-medium text-blue-700 rounded-full bg-blue-50/80 border border-blue-100/80">
            {t('home.faq.commonQuestions', 'Common Questions')}
          </span>

          <h2 className="mb-4 text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 tracking-tight">
            {t('home.faq.title', 'Frequently Asked Questions')}
          </h2>

          <p className="text-lg sm:text-xl text-gray-600">
            {t('home.faq.subtitle', 'Get answers to the most commonly asked questions about FluxBuilder')}
          </p>
        </div>

        {/* Category Filter Tabs */}
        {!customFaqs && (
          <div className="max-w-3xl mx-auto mb-8">
            <div className="flex justify-center mb-8">
              <div className="inline-flex bg-gray-100 rounded-xl p-1">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => {
                      setActiveCategory(category.id);
                      setOpenIndex(0); // Open first item of new category
                    }}
                    className={`relative px-4 sm:px-6 py-2 sm:py-2.5 text-sm sm:text-base font-medium rounded-lg transition-all duration-200 ${
                      activeCategory === category.id
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {category.name}
                    {activeCategory === category.id && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-blue-600 rounded-full"></div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="max-w-3xl mx-auto overflow-hidden bg-white border border-gray-100 rounded-xl divide-y divide-gray-100">
          {currentFaqs.map((faq, index) => (
            <FAQItem
              key={`${activeCategory}-${index}`}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              toggleOpen={toggleOpen}
              hideBorder={index === currentFaqs.length - 1}
              index={index}
            />
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="mb-6 text-lg text-gray-600">
            {t('home.faq.stillHaveQuestions', 'Still have questions?')}
          </p>
          <a
            href="https://support.fluxbuilder.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-all duration-200 hover:scale-[1.02] text-base"
          >
            {t('home.faq.contactSupport', 'Contact Support')}
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
