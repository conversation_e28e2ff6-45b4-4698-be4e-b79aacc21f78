import Link from "next/link";
import TrustpilotReviews from './TrustpilotReviews';
import StatCard from '../components/StatCard';
import IconSVG from '../components/IconSVG';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from "../../lib/utils/i18n";

const TrustAndSocialProofSection = () => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Define stats data for reusability
  const statsData = [
    {
      icon: <IconSVG type="rocket" />,
      value: "50k+",
      label: t('home.trust.appsPublished')
    },
    {
      icon: <IconSVG type="star" />,
      value: "98%",
      label: t('home.trust.starReviews')
    },
    {
      icon: <IconSVG type="check" />,
      value: "1,151",
      label: t('home.trust.verifiedReviews')
    },
    {
      icon: <IconSVG type="support" />,
      value: "24/7",
      label: t('home.trust.expertSupport')
    }
  ];

  return (
    <section ref={sectionRef} className="py-16 md:py-20 lg:py-24 bg-slate-100 ">
      <div className=" px-4 mx-auto">
        <div className="mb-10 container md:mb-14 text-center">
          <div className={`transition-opacity duration-500 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
            <span className="inline-block px-3 py-1 mb-3 text-xs font-medium text-green-600 rounded-full bg-green-50 border border-green-100">
              {t('home.trust.title')}
            </span>
          </div>

          <div className={`transition-all duration-500 delay-100 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="mb-3 text-2xl sm:text-3xl font-bold text-gray-900 md:text-4xl">
              {t('home.hero.trustedBy')}
            </h2>
          </div>

          <div className={`transition-all duration-500 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <p className="max-w-2xl mx-auto text-base sm:text-lg text-gray-600">
              {t('home.trust.subtitle')}
            </p>
          </div>
        </div>

        {/* Stats Cards - Clean grid */}
        <div className="grid  max-w-5xl grid-cols-2 gap-4 md:gap-6 mx-auto mb-10 lg:grid-cols-4">
          {statsData.map((stat, index) => (
            <div 
              key={index}
              className={`transition-all duration-500 delay-${index * 100} ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
            >
              <div className="flex flex-col items-center p-4 md:p-6 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-colors duration-200">
                <div className="flex items-center justify-center w-12 h-12 mb-4 text-blue-500 bg-blue-50 rounded-full border border-blue-100">
                  {stat.icon}
                </div>
                <div className="text-2xl md:text-3xl font-bold text-gray-900">{stat.value}</div>
                <div className="mt-1 text-sm text-gray-600 text-center">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Trustpilot Reviews */}
        <div className={`mx-auto mb-8 transition-opacity duration-500 delay-500 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
          <TrustpilotReviews />
        </div>

        {/* CTA Button */}
        <div className="text-center mt-8">
          <div className={`transition-all duration-500 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Link
              href="/showcase"
              className="btn btn-cta"
            >
              {t('home.trust.viewShowcase')}
              <span className="ml-2">
                <IconSVG type="arrow" />
              </span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustAndSocialProofSection;