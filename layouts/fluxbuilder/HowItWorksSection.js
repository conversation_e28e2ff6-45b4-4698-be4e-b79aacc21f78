import Image from 'next/image';
import { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslation } from "../../lib/utils/i18n";

const HowItWorksSection = () => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      number: '1',
      key: 'connect',
      image: '/images/home/<USER>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      number: '2',
      key: 'customize',
      image: '/images/home/<USER>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
        </svg>
      )
    },
    {
      number: '3',
      key: 'publish',
      image: '/images/home/<USER>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      )
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Auto-cycle through steps when the section is visible
    if (!isVisible) return;

    const intervalId = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 4000);

    return () => clearInterval(intervalId);
  }, [isVisible]);

  return (
    <section ref={sectionRef} className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
      <div className="container px-3 mx-auto sm:px-6 lg:px-8">
        <div className="mx-auto">
          <div className="mb-8 sm:mb-12 md:mb-16 text-center">
            <motion.span
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6 }}
              className="inline-block px-3 py-1 mb-3 sm:mb-4 text-xs sm:text-sm font-medium text-blue-600 bg-blue-50 rounded-full"
            >
              {t('home.howItWorks.title')}
            </motion.span>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-3 sm:mb-4 text-2xl sm:text-3xl font-bold text-gray-900 md:text-4xl letter-tight"
            >
              {t('home.howItWorks.title')}
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="max-w-2xl mx-auto mb-6 sm:mb-10 text-base sm:text-lg text-gray-600"
            >
              {t('home.howItWorks.subtitle')}
            </motion.p>

            {/* Enhanced process indicator - Desktop */}
            <div className="relative items-center justify-between hidden max-w-3xl mx-auto mb-10 sm:mb-16 md:flex">
              {/* Progress bar */}
              <div className="absolute z-0 w-full h-1 top-[20px] bg-gray-100">
                <motion.div
                  className="h-full bg-blue-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${(activeStep / (steps.length - 1)) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                ></motion.div>
              </div>

              {/* Step indicators */}
              {steps.map((step, index) => (
                <div
                  key={index}
                  className="z-10 flex flex-col items-center"
                  onClick={() => setActiveStep(index)}
                >
                  <motion.div
                    className={`relative flex items-center justify-center w-10 h-10 md:w-12 md:h-12 rounded-full cursor-pointer mb-3 ${index <= activeStep ? 'bg-blue-500 text-white' : 'bg-white text-gray-400 border-2 border-gray-200'}`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    animate={{
                      scale: index === activeStep ? [1, 1.1, 1] : 1,
                      boxShadow: index <= activeStep ? '0 0 0 4px rgba(59, 130, 246, 0.3)' : 'none'
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    {step.icon}
                    {index <= activeStep && (
                      <motion.div
                        className="absolute w-full h-full bg-blue-500 rounded-full -z-10"
                        initial={{ scale: 0, opacity: 1 }}
                        animate={{ scale: index === activeStep ? 1.5 : 1, opacity: index === activeStep ? 0 : 1 }}
                        transition={{ duration: 1, repeat: index === activeStep ? Infinity : 0, repeatDelay: 1 }}
                      />
                    )}
                  </motion.div>
                  <span className={`text-xs sm:text-sm font-medium ${index <= activeStep ? 'text-blue-600' : 'text-gray-400'}`}>
                    {t(`home.howItWorks.step${step.number}`)}
                  </span>
                </div>
              ))}
            </div>

            {/* Mobile process indicator - now visible */}
            <div className="hidden">
              <div className="flex items-center justify-center space-x-3 mb-2">
                {steps.map((step, index) => (
                  <motion.div
                    key={index}
                    className={`w-2.5 h-2.5 rounded-full cursor-pointer ${index === activeStep ? 'bg-blue-500' : 'bg-gray-200'}`}
                    onClick={() => setActiveStep(index)}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                    animate={{
                      scale: index === activeStep ? [1, 1.3, 1] : 1,
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    {index === activeStep && (
                      <motion.div
                        className="absolute w-full h-full bg-blue-500 rounded-full -z-10"
                        initial={{ scale: 1, opacity: 0.5 }}
                        animate={{ scale: 2, opacity: 0 }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      />
                    )}
                  </motion.div>
                ))}
              </div>
              <div className="text-sm font-medium text-blue-600">
                {t(`home.howItWorks.step${activeStep + 1}`)}: {t(`home.howItWorks.steps.${steps[activeStep].key}.title`)}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:gap-6 md:gap-8 lg:grid-cols-3">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: isVisible ? 1 : 0,
                  y: isVisible ? 0 : 20
                }}
                transition={{
                  duration: 0.5,
                  delay: index * 0.15,
                  ease: "easeOut"
                }}
                onMouseEnter={() => setActiveStep(index)}
                className="h-full"
              >
                <motion.div
                  className={`flex flex-col cursor-pointer h-full border rounded-xl ${activeStep === index ? 'border-blue-200 bg-blue-50/30' : 'border-gray-100 bg-white'}`}
                  animate={{
                    boxShadow: activeStep === index ? '0 8px 30px rgba(59, 130, 246, 0.15)' : '0 1px 3px rgba(0, 0, 0, 0.05)',
                    y: activeStep === index ? -5 : 0
                  }}
                  transition={{ duration: 0.3 }}
                  whileHover={{ y: -5, boxShadow: '0 15px 35px rgba(59, 130, 246, 0.2)' }}
                >
                  {/* Step header - mobile optimized */}
                  <div className="flex items-center border-b border-gray-100 p-3 sm:p-4 md:p-6">
                    <div className={`flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full mr-2 sm:mr-3 md:mr-4 text-sm ${activeStep === index ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-500'}`}>
                      {step.icon}
                    </div>
                    <h3 className="text-base sm:text-lg md:text-xl font-semibold text-gray-900 letter-tight">
                      {t(`home.howItWorks.steps.${step.key}.title`)}
                    </h3>
                  </div>

                  {/* Step content - mobile optimized */}
                  <div className="flex flex-col flex-1 gap-3 sm:gap-4 md:gap-6 p-3 sm:p-4 md:p-6">
                    <p className="text-xs sm:text-sm md:text-base text-gray-600">{t(`home.howItWorks.steps.${step.key}.description`)}</p>
                    <div className="relative mt-auto overflow-hidden bg-gray-100 rounded-lg shadow-sm sm:shadow-md aspect-[4/3] md:aspect-video">
                      <Image
                        src={step.image}
                        alt={t(`home.howItWorks.steps.${step.key}.title`)}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>

          {/* Call to action - mobile optimized */}
          <div className="mt-8 sm:mt-12 md:mt-16 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
              transition={{ duration: 0.5, delay: steps.length * 0.15 + 0.2 }}
            >
              <Link
                href="/pricing"
                className="btn btn-sm btn-cta"
              >
                <span>{t('home.howItWorks.startNow')}</span>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </motion.div>
            <p className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">
              {t('home.howItWorks.noCreditCard')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;