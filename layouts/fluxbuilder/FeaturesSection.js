import { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import { useTranslation } from "../../lib/utils/i18n";

const FeaturesSection = () => {
  const { t } = useTranslation();

  const features = [
    {
      key: 'quickSetup',
      image: '/images/features/drag-and-drop.png',
      badge: t('home.features.badges.popular'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      key: 'stunningDesign',
      image: '/images/home/<USER>',
      badge: t('home.features.badges.new'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
        </svg>
      )
    },
    {
      key: 'advancedFeatures',
      image: '/images/home/<USER>',
      badge: t('home.features.badges.premium'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      )
    },
    {
      key: 'multiPlatform',
      image: '/images/features/ios-android.png',
      badge: t('home.features.badges.essential'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
        </svg>
      )
    },
    {
      key: 'performanceOptimized',
      image: '/images/features/flutter.png',
      badge: t('home.features.badges.fast'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      key: 'seamlessUpdates',
      image: '/images/features/live-preview.png',
      badge: t('home.features.badges.timeSaver'),
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      )
    }
  ];

  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section ref={sectionRef} className="relative py-24 md:py-28 lg:py-32 bg-gradient-to-b from-white to-gray-50/50">
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute w-[800px] h-[800px] -top-[400px] -right-[400px] bg-gradient-to-br from-blue-50/40 to-indigo-50/40 rounded-full blur-3xl"></div>
        <div className="absolute w-[600px] h-[600px] -bottom-[300px] -left-[300px] bg-gradient-to-tr from-blue-50/40 to-indigo-50/40 rounded-full blur-2xl"></div>
      </div>

      <div className="container relative px-4 mx-auto">
        <div className="max-w-4xl mx-auto mb-16 text-center">
          <div className={`transition-opacity duration-500 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
            <span className="inline-block px-3 py-1 mb-4 text-xs font-medium text-blue-700 rounded-full bg-blue-50/80 border border-blue-100/80">
              {t('home.features.badge', 'Key Features')}
            </span>
          </div>

          <div className={`transition-all duration-500 delay-100 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <h2 className="mb-3 text-2xl sm:text-3xl font-bold text-gray-900 md:text-4xl">
              {t('home.features.title')}
            </h2>
          </div>

          <div className={`transition-all duration-500 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <p className="text-lg sm:text-xl text-gray-600">
              {t('home.features.subtitle')}
            </p>
          </div>
        </div>

        {/* Features grid with images */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              <div className="group relative flex flex-col h-full overflow-hidden bg-white border border-gray-200 rounded-xl hover:shadow-md transition-all duration-200">
                {/* Feature Badge */}
                <div className="relative">
                  {feature.badge && (
                    <div className="absolute z-10 top-3 right-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                        feature.badge === t('home.features.badges.new') ? 'bg-green-50 text-green-700 border-green-100' :
                        feature.badge === t('home.features.badges.popular') ? 'bg-blue-50 text-blue-700 border-blue-100' :
                        feature.badge === t('home.features.badges.premium') ? 'bg-purple-50 text-purple-700 border-purple-100' :
                        feature.badge === t('home.features.badges.essential') ? 'bg-indigo-50 text-indigo-700 border-indigo-100' :
                        feature.badge === t('home.features.badges.fast') ? 'bg-red-50 text-red-700 border-red-100' :
                        'bg-amber-50 text-amber-700 border-amber-100'
                      }`}>
                        {feature.badge}
                      </span>
                    </div>
                  )}

                  {/* Feature Image */}
                  <div className="relative h-64 overflow-hidden bg-gray-50">
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-900/5 to-gray-900/0"></div>
                    <Image
                      src={feature.image}
                      alt={t(`home.features.items.${feature.key}.title`)}
                      width={800}
                      height={600}
                      className="object-contain w-full h-full transition-transform duration-700 group-hover:scale-105"
                    />
                  </div>
                </div>

                {/* Feature Content */}
                <div className="flex flex-col flex-grow p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 mr-4 rounded-lg bg-gray-50 border border-gray-100 transition-colors duration-200 group-hover:bg-blue-50/50 group-hover:border-blue-100">
                      {feature.icon}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900">
                      {t(`home.features.items.${feature.key}.title`)}
                    </h3>
                  </div>
                  <p className="text-base text-gray-600 leading-relaxed">
                    {t(`home.features.items.${feature.key}.description`)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="mt-16 text-center">
            <a
              href="/features"
              className="btn btn-cta"
            >
              {t('home.features.viewAllFeatures')}
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;