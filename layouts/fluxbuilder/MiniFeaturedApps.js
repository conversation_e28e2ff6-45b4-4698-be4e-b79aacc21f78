import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import ScrollingList from '@/components/ui/ScrollingList';
import ImageFallback from '@/layouts/components/ImageFallback';
import { dateFormat } from "../../lib/utils/dateFormat";
import { useTranslation } from "../../lib/utils/i18n";
import { markStart, markEnd, useComponentPerformance, logMetrics } from "../../lib/utils/performance";
import { useLazyImages } from "../../lib/hooks/useLazyImages";

// Extracted AppCard component for cleaner code
const AppCard = ({ app, initialIndex }) => {
  const { t } = useTranslation();
  const { ref, isVisible } = useLazyImages({ rootMargin: '200px' });

  // Generate app URL using slug (matches existing app page structure)
  const appUrl = `/app/${app.slug}`;

  // Performance optimization - preload first few cards immediately
  const [screenshotsLoaded, setScreenshotsLoaded] = useState(initialIndex < 3);

  useEffect(() => {
    if (isVisible && !screenshotsLoaded) {
      markStart(`AppCard:loadScreenshots:${app.frontmatter.bundleId}`);
      // Load screenshots immediately when visible
      setScreenshotsLoaded(true);
      markEnd(`AppCard:loadScreenshots:${app.frontmatter.bundleId}`);
    }
  }, [isVisible, screenshotsLoaded, app.frontmatter.bundleId]);

  return (
    <div
      ref={ref}
      className="relative flex flex-col w-full h-full mx-auto overflow-hidden transition-all duration-300 transform bg-white border border-gray-200 rounded-xl hover:shadow-md group"
    >
        {/* See More button - appears when hovering anywhere on the card */}
        <Link
          href={appUrl}
          className="absolute bottom-4 right-4 z-10 px-4 py-2 text-sm font-medium  rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 text-blue-600"
        >
          {t('home.trust.readMore', 'See More')} →
        </Link>
        {/* App Screenshots Preview */}
        <div className="relative overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800 h-[200px] md:h-[240px] flex-shrink-0">
          {/* Glass effect overlay */}
          <div className="absolute inset-0 bg-white/5 backdrop-blur-[0.5px]"></div>

          {/* Decorative elements - minimalist version */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div className="absolute w-32 h-32 rounded-full -top-8 -left-8 bg-white/5"></div>
            <div className="absolute rounded-full w-40 h-40 -bottom-10 -right-10 bg-white/5"></div>
          </div>

          <div className="absolute inset-0 flex items-center justify-center">
            {!isVisible ? (
              // Placeholder while not visible in viewport - minimal design
              <div className="flex items-center justify-center gap-4">
                <div className="w-20 h-32 rounded-lg bg-white/5 animate-pulse"></div>
                <div className="w-20 h-32 delay-100 rounded-lg bg-white/5 animate-pulse"></div>
                <div className="w-20 h-32 delay-200 rounded-lg bg-white/5 animate-pulse"></div>
              </div>
            ) : screenshotsLoaded && app.frontmatter.iosScreen && app.frontmatter.iosScreen[0] ? (
              <div className="relative w-full h-full overflow-hidden">
                {/* Parallax scrolling container */}
                <div className={`absolute inset-0 flex items-center justify-center gap-4 animate-screenshotScroll`}>
                  {/* Render at least 3 screenshots for better visual appearance */}
                  {[...app.frontmatter.iosScreen.slice(0, 3), ...app.frontmatter.iosScreen.slice(0, 3)].map((screen, index) => (
                    <div
                      key={index}
                      className={`flex-shrink-0 transition-all duration-500 ease-in-out hover:scale-105 ${index % 2 === 0 ? 'translate-y-[5px]' : 'translate-y-[-5px]'}`}
                    >
                      <div className="relative overflow-hidden rounded-lg border border-white/10">
                        <ImageFallback
                          src={screen}
                          width={120}
                          height={240}
                          className="object-contain h-auto w-auto max-h-[220px]"
                          alt={`${app.frontmatter.app_name} ${t('home.showcase.screenshot', 'screenshot')} ${index + 1}`}
                          useLanguageSpecific={true}
                          unoptimized={true}
                        />
                        {/* Subtle reflection effect */}
                        <div className="absolute inset-0 opacity-20 bg-gradient-to-t from-white/20 to-transparent"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : screenshotsLoaded && app.frontmatter.androidScreens && app.frontmatter.androidScreens[0] ? (
              <div className="relative w-full h-full overflow-hidden">
                {/* Parallax scrolling container */}
                <div className={`absolute inset-0 flex items-center justify-center gap-4 animate-screenshotScroll`}>
                  {/* Render at least 3 screenshots for better visual appearance */}
                  {[...app.frontmatter.androidScreens.slice(0, 3), ...app.frontmatter.androidScreens.slice(0, 3)].map((screen, index) => (
                    <div
                      key={index}
                      className={`flex-shrink-0 transition-all duration-500 ease-in-out hover:scale-105 ${index % 2 === 0 ? 'translate-y-[5px]' : 'translate-y-[-5px]'}`}
                    >
                      <div className="relative overflow-hidden rounded-lg border border-white/10">
                        <ImageFallback
                          src={screen}
                          width={120}
                          height={240}
                          className="object-contain h-auto w-auto max-h-[220px]"
                          alt={`${app.frontmatter.app_name} ${t('home.showcase.screenshot', 'screenshot')} ${index + 1}`}
                          useLanguageSpecific={true}
                          unoptimized={true}
                        />
                        {/* Subtle reflection effect */}
                        <div className="absolute inset-0 opacity-20 bg-gradient-to-t from-white/20 to-transparent"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="px-4 py-2 font-medium text-white/90 rounded-md bg-white/5 backdrop-blur-sm border border-white/10">
                {isVisible && screenshotsLoaded
                  ? t('home.showcase.noScreenshots', 'No screenshots available')
                  : t('home.showcase.loading', 'Loading...')}
              </div>
            )}
          </div>
        </div>

        {/* App Info */}
        <div className="flex flex-col flex-1 p-5 md:p-6">
          <div className="flex-shrink-0">
            <div className="flex flex-wrap gap-2 mb-3">
              <div className="px-3 py-1 text-xs font-medium tracking-wide text-blue-700 bg-blue-50 border border-blue-100 rounded-full">
                {app.frontmatter.cms && app.frontmatter.cms[0]}
              </div>
              {app.frontmatter.category && app.frontmatter.category.slice(0, 1).map((cat, i) => (
                <div key={i} className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-100 rounded-full">
                  {cat}
                </div>
              ))}
            </div>

            <h3 className="mb-2 text-lg font-bold text-gray-900">{app.frontmatter.app_name}</h3>

            <div className="mb-3 text-xs text-gray-500">
              {t('home.showcase.updated', 'Updated')}: {dateFormat(app.frontmatter.date)}
            </div>
          </div>

          <div className="flex flex-col flex-1">
            <p className="overflow-hidden text-sm text-gray-600 line-clamp-2">
              {app.content}
            </p>
          </div>
        </div>
      </div>
  );
};

const MiniFeaturedApps = ({ featuredExamples }) => {
  const { t } = useTranslation();
  const [isClient, setIsClient] = useState(false);

  // Performance monitoring
  const perf = useComponentPerformance('MiniFeaturedApps');

  // Set isClient to true once component mounts
  useEffect(() => {
    perf.markMount();
    markStart('MiniFeaturedApps:init');

    setIsClient(true);

    markEnd('MiniFeaturedApps:init');

    return () => {
      perf.markUnmount();
    };
  }, []);

  if (!featuredExamples?.length) return null;

  // Sort featuredExamples by date (most recent first)
  const sortedExamples = useMemo(() => {
    markStart('MiniFeaturedApps:sortExamples');

    const sorted = [...featuredExamples].sort((a, b) => {
      // Handle potential invalid dates by using a fallback
      let dateA = new Date(a.frontmatter.date);
      let dateB = new Date(b.frontmatter.date);

      // Check if dates are valid, if not use a fallback date
      if (isNaN(dateA.getTime())) dateA = new Date(0); // Jan 1, 1970
      if (isNaN(dateB.getTime())) dateB = new Date(0); // Jan 1, 1970

      return dateB - dateA; // Sort in descending order (newest first)
    });

    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`Sorted ${sorted.length} featured examples for display`);
    }
    markEnd('MiniFeaturedApps:sortExamples');
    return sorted;
  }, [featuredExamples]);

  // Log performance metrics after component is fully rendered
  useEffect(() => {
    if (isClient) {
      setTimeout(() => {
        logMetrics();
      }, 1000);
    }
  }, [isClient]);

  // Return a placeholder during SSR
  if (!isClient) {
    return (
      <section className="bg-indigo-100 py-14">
        <div className="mx-auto ">
          <div className="text-center">
            <h2 className="mb-4 text-3xl font-bold text-gray-900">{t('home.showcase.title', 'App Showcase')}</h2>
            <p className="max-w-2xl mx-auto text-lg text-gray-600">{t('home.showcase.subtitle', 'See real-world examples of mobile apps built and launched with FluxBuilder')}</p>
          </div>
          <div className="flex justify-center items-center min-h-[500px]">
            <div className="w-12 h-12 border-4 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 md:py-28 lg:py-32 bg-neutral-100">
      <div className="px-4 mx-auto">
        <div className="mb-12 text-center">
          <span className="inline-block px-3 py-1 mb-4 text-xs font-medium text-blue-700 rounded-full bg-blue-50/80 border border-blue-100/80">
            {t('home.showcase.badge', 'Featured Apps')}
          </span>
          <h2 className="mb-4 text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 tracking-tight">
            {t('home.showcase.title', 'App Showcase')}
          </h2>
          <p className="max-w-2xl mx-auto text-lg sm:text-xl text-gray-600">
            {t('home.showcase.subtitle', 'See real-world examples of mobile apps built and launched with FluxBuilder')}
          </p>
        </div>

        {/* Enhanced App Cards Carousel */}
        <div className="relative mb-12">
          {isClient && (
            <ScrollingList
              className="py-8 min-h-[520px] -mx-4 px-4"
              itemClassName=""
              containerStyle={{
                gap: '1rem',
                paddingBottom: '1.25rem',
                paddingLeft: '0.5rem',
                paddingRight: '0.5rem',
                width: '100%',
              }}
              itemStyle={{
                width: '330px',
                height: '480px',
                flexShrink: 0,
              }}
              enableLerp={true}
              lerpFactor={0.08}
              dragMultiplier={2}
            >
              {/* First set of examples - limit to 10 for better performance */}
              {sortedExamples.slice(0, 10).map((app, index) => (
                <AppCard
                  key={index}
                  app={app}
                  initialIndex={index}
                />
              ))}
              {/* Duplicate set for seamless looping - also limited to 10 */}
              {sortedExamples.slice(0, 10).map((app, index) => (
                <AppCard
                  key={`duplicate-${index}`}
                  app={app}
                  initialIndex={index + 10}
                />
              ))}
            </ScrollingList>
          )}
        </div>

        {/* View All Apps Button */}
        <div className="text-center">
          <Link
            href="/showcase"
            className="inline-flex items-center px-6 py-3 text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-all duration-200 hover:scale-[1.02] text-base"
          >
            {t('home.showcase.viewAll', 'View All Apps')}
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MiniFeaturedApps;
