import { useState, useEffect, useMemo } from 'react';
import ImageFallback from "@/layouts/components/ImageFallback";
import Link from "next/link";
import ScrollingList from '@/components/ui/ScrollingList';
import { useOptimizedLanguageContext } from "../../context/optimizedLanguageContext";

// Helper function to get initials and background color
const getAvatarData = (name) => {
  const firstLetter = name?.charAt(0)?.toUpperCase() || '?';
  // Generate a consistent color based on the name
  const colors = [
    'bg-blue-100 text-blue-600',
    'bg-green-100 text-green-600',
    'bg-purple-100 text-purple-600',
    'bg-pink-100 text-pink-600',
    'bg-yellow-100 text-yellow-600',
    'bg-indigo-100 text-indigo-600'
  ];
  const colorIndex = name?.length % colors.length || 0;
  return { letter: firstLetter, colorClass: colors[colorIndex] };
};

const TrustpilotReviews = () => {
  const { t } = useOptimizedLanguageContext();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Use memoization to prevent unnecessary re-renders when translations change
  const reviews = useMemo(() => [
    {
      quote: t('home.trust.reviews.review1', 'FluxBuilder is amazing! It helped me create a professional mobile app for my business in no time.'),
      author: "Karthikeyan Srinivasan",
      author_image: "https://user-images.trustpilot.com/67e88398b0327af2ed2f6465/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/67e883a036fed695cd88019f"
    },
    {
      quote: t('home.trust.reviews.review2', 'The app works flawlessly and the customer support is excellent. Highly recommended!'),
      author: "Yashar Haque",
      author_image: "https://user-images.trustpilot.com/67ee3c4e05c902aeff5c697e/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/67ee3c5641301df6d60c8ddf"
    },
    {
      quote: t('home.trust.reviews.review3', 'As a developer, I appreciate the clean code and well-designed architecture of FluxBuilder.'),
      author: "Ertekin AÇIL",
      company: "developer",
      link: "https://www.trustpilot.com/reviews/67df44b36c408eaf96db2ba1"
    },
    {
      quote: t('home.trust.reviews.review4', 'FluxBuilder has transformed our e-commerce business with a native mobile app that our customers love.'),
      author: "Jerko",
      company: "ecommerceBusiness",
      link: "https://www.trustpilot.com/reviews/67dbbbbf11eebea1fdb3e29b"
    },
    {
      quote: t('home.trust.reviews.review5', 'The app creation process was smooth and the result exceeded our expectations.'),
      author: "Daniele Spadafora",
      author_image: "https://user-images.trustpilot.com/67d9740ec88843cb9d065217/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/67d974197dd0f09a8aae4c38"
    },
    {
      quote: t('home.trust.reviews.review6', 'FluxBuilder saved us thousands of dollars in development costs. Great value for money!'),
      author: "Mojtaba Oboudi",
      author_image: "https://user-images.trustpilot.com/678a62317d60905b818883d9/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/678a623b5b04f4eddbab85d5"
    },
    {
      quote: t('home.trust.reviews.review7', 'Our customers are delighted with our new mobile app. Thanks FluxBuilder!'),
      author: "Carlo odasso",
      company: "businessOwner",
      author_image: "https://user-images.trustpilot.com/676ac6ab7dbbd07cd22c58b3/73x73.png",
      link: "https://www.trustpilot.com/reviews/676ac6b2e0b0c434e155b468"
    },
    {
      quote: t('home.trust.reviews.review8', 'The app performance is excellent and the user experience is top-notch.'),
      author: "Hosam Ali",
      author_image: "https://user-images.trustpilot.com/67229c63c79ffb13771a9d11/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/67229c69cd6370b39dd17724"
    },
    {
      quote: t('home.trust.reviews.review9', 'FluxBuilder made it easy to convert our website into a beautiful mobile app.'),
      author: "Benjamin Selmani",
      author_image: "https://user-images.trustpilot.com/66a77c7d9092bcb9fa89f026/73x73.png",
      company: "businessOwner",
      link: "https://www.trustpilot.com/reviews/66a77c8246d84f4b682f9582"
    }
  ], [t]); // Only re-create the array when the translation function changes

  return (
    <div className="py-0">
      {isClient && (

          <ScrollingList
            className=" py-4"
            containerStyle={{
              gap: '1rem',
              width: '100%',
            }}
            itemStyle={{
              minWidth: '320px',
              maxWidth: '380px',
              flexShrink: 0,
            }}
            enableLerp={true}
            lerpFactor={0.05}
            dragMultiplier={2}
          >
            {[...reviews, ...reviews].map((review, index) => (
              <a
                key={index}
                href={review.link}
                target="_blank"
                rel="noopener noreferrer"
                className={`block p-7 group bg-white border border-gray-200 dark:bg-gray-700 rounded-xl transition-all duration-500 ease-in-out group-hover:shadow-md relative ${
                  index % 2 === 0 ? 'translate-y-[10px]' : ''
                } cursor-pointer h-full`}
              >
                  {/* Add a subtle overlay effect on hover */}
                  <div className="absolute inset-0 bg-black opacity-0 transition-opacity duration-300 rounded-xl"></div>

                  <div className="flex flex-col items-center mb-6">
                    <div className="mb-3">
                      {review.author_image ? (
                        <img
                          src={review.author_image}
                          alt={review.author}
                          className="w-16 h-16 rounded-full shadow-sm"
                        />
                      ) : (
                        <div className={`w-16 h-16 rounded-full flex items-center justify-center text-xl font-semibold shadow-sm ${getAvatarData(review.author).colorClass}`}>
                          {getAvatarData(review.author).letter}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center mb-2">
                      <ImageFallback
                        src="images/stars-5.svg"
                        alt="5 stars"
                        width={100}
                        height={22}
                        style={{ width: 'auto', height: '22px' }}
                      />
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-gray-800 dark:text-gray-200">{review.author}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {t(`home.trust.${review.company.toLowerCase().replace(' ', '')}`) || t('home.trust.businessOwner')}
                      </div>
                    </div>
                  </div>

                  <blockquote className="mb-6 text-sm text-gray-700 dark:text-gray-200 line-clamp-5 text-center">
                    "{review.quote}"
                  </blockquote>

                  {/* Add a subtle "Read review" link that appears on hover */}
                  <div className="flex justify-end"> 
                    <span className="text-sm text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center gap-1">
                      {t('home.trust.readMore')}
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </span>
                  </div>
                </a>

            ))}
          </ScrollingList>
      )}

      <div className="flex flex-col items-center justify-center gap-4 pb-2 mt-8 md:flex-row md:items-center">
        <Link
          href="https://www.trustpilot.com/review/inspireui.com"
          target="_blank"
          rel="noopener noreferrer"
          className="px-5 py-3 text-sm font-medium text-gray-600 transition-all duration-300 rounded-full hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 backdrop-blur-sm"
        >
        <div className="flex items-center gap-4">
          <div className="flex items-center">
            <ImageFallback
              src="/images/brands/trustpilot.svg"
              alt="Trustpilot"
              width={120}
              height={30}
              style={{ width: 'auto', height: '30px' }}
            />
          </div>
          <span className="mt-2 text-sm font-medium text-gray-600">{t('home.trust.verifiedReviewsLabel')}</span>
        </div>
        </Link>
      </div>
    </div>
  );
};

export default TrustpilotReviews;