import { motion, useAnimation } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from "../../lib/utils/i18n";
import SimpleParallaxEffect from '../components/SimpleParallaxEffect';
import Link from "next/link";
import { AuroraText } from '@/components/ui/AuroraText';

const FinalCTASection = () => {
  const { t } = useTranslation();
  // State to track visibility for animations
  const [, setIsVisible] = useState(false);
  const sectionRef = useRef(null);
  const controls = useAnimation();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          controls.start('visible');
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [controls]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section ref={sectionRef} className="relative py-12 sm:py-16 md:py-20 overflow-hidden bg-[#000] final-cta-section">
      {/* Base background with gradient - optimized for mobile */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-[#000]" />

        {/* Additional glow effects - reduced size on mobile */}
        <div className="absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none z-[2]">
          <div className="absolute rounded-full w-[20rem] sm:w-[30rem] md:w-[40rem] h-[20rem] sm:h-[30rem] md:h-[40rem] top-20 -right-[10rem] sm:-right-[15rem] md:-right-[20rem] bg-blue-500/20 blur-[50px] sm:blur-[75px] md:blur-[100px]"></div>
          <div className="absolute rounded-full w-[20rem] sm:w-[30rem] md:w-[40rem] h-[20rem] sm:h-[30rem] md:h-[40rem] bottom-20 -left-[10rem] sm:-left-[15rem] md:-left-[20rem] bg-indigo-500/20 blur-[50px] sm:blur-[75px] md:blur-[100px]"></div>
        </div>

        {/* Subtle grid pattern overlay */}
        <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] opacity-5 z-[3]"></div>
      </div>

      {/* Parallax Effect - reduced height on mobile */}
      <div className="absolute inset-0 top-[-20px] sm:top-[-25px] md:top-[-30px] z-[4] flex">
        <div className="relative w-full max-w-4xl h-[300px] sm:h-[400px] md:h-[500px] mx-auto">
          <SimpleParallaxEffect />
        </div>
      </div>

      <motion.div
        className="relative z-[5] px-4 sm:px-6 pt-[250px] sm:pt-[300px] md:pt-[350px] mx-auto text-center max-w-7xl lg:px-8"
        variants={containerVariants}
        initial="hidden"
        animate={controls}
      >
        {/* Enhanced headline - mobile optimized with improved readability */}
        <motion.h2
          className="max-w-3xl mx-auto text-2xl font-bold tracking-tight text-white sm:text-3xl md:text-4xl lg:text-5xl"
          style={{ lineHeight: '58px' }}
          variants={itemVariants}
        >
          <span className="block">Launch your mobile app</span>
          <span className="block">
            <AuroraText speed={0.4}>in minutes</AuroraText>, not months
          </span>
        </motion.h2>

        {/* Enhanced subheading - mobile optimized */}
        <motion.p
          className="max-w-2xl mx-auto mt-3 text-base leading-7 text-blue-100 sm:mt-4 md:mt-6 sm:text-lg md:text-xl sm:leading-8"
          variants={itemVariants}
        >
          {t('home.cta.subtitle')}
        </motion.p>

        {/* Simplified CTA section - mobile optimized */}
        <motion.div
          className="mt-6 sm:mt-8 md:mt-12"
          variants={itemVariants}
        >
          {/* Rainbow CTA button */}
          <div className="flex flex-col items-center justify-center">
            <Link
              href="/download"
              className="btn btn-cta"
            >
              {t('home.cta.button')}
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default FinalCTASection;
